/* -*- linux-c -*- */
/*******************************************************************************
*               Copyright 2009, Marvell Technology Group Ltd.
*
* THIS CODE CONTAINS CONFIDENTIAL INFORMATION OF MARVELL. NO RIGHTS ARE GRANTED
* HEREIN UNDER ANY PATENT, MASK WORK RIGHT OR COPYRIGHT OF MARVELL OR ANY THIRD
* PARTY. MARVELL RESERVES THE RIGHT AT ITS SOLE DISCRETION TO REQUEST THAT THIS
* CODE BE IMMEDIATELY RETURNED TO MARVELL. THIS CODE IS PROVIDED "AS IS".
* MARVELL MAKES NO WARRANTIES, EXPRESS, IMPLIED OR OTHERWISE, REGARDING ITS
* ACCURACY, COMPLETENESS OR PERFORMANCE. MARVELL COMPRISES MARVELL TECHN<PERSON>OGY
* GROUP LTD. (MTGL) AND ITS SUBSIDIARIES, MARVELL INTERNATIONAL LTD. (MIL),
* MARVELL TECHNOLOGY, INC. (MTI), MARVELL SEMICONDUCTOR, INC. (MSI), MARVELL
* ASIA PTE LTD. (MAPL), MARVELL JAPAN K.K. (MJKK), GALILEO TECHNOLOGY LTD. (GTL)
* GALILEO TECHNOLOGY, INC. (GTI) AND RADLAN Computer Communications, LTD.
********************************************************************************
*/

#include <stdio.h>
#include <stdlib.h>
//#include <linux/limits.h>

#include "duster_applets.h"
unsigned int module_maxnumber=27;

extern struct applet_info module_applet_array[];
#if 0
void duster_call_module_run_cmd(char *cmdname, int flags, char *opaque)
{
	char command[PATH_MAX];
	char *action = NULL;

	switch (flags) {
	case DUSTER_CONFIG_START:
		action = "start";
		break;
	case DUSTER_CONFIG_STOP:
		action = "stop";
		break;
	case DUSTER_CONFIG_SET:
		action = "set";
		break;
	case DUSTER_CONFIG_GET:
		action = "get";
		break;
	case DUSTER_CONFIG_GETLIST:
		action = "getlist";
		break;
	case DUSTER_CONFIG_SETLIST:
		action = "setlist";
		break;
	default:
		/* Do nothing */
		return;
		break;
	}

	if (action) {
		if (opaque) {
			snprintf(command, PATH_MAX, "%s %s %s", cmdname, action, opaque);
		} else {
			snprintf(command, PATH_MAX, "%s %s", cmdname, action);
		}
		system(command);
	}
}
#endif 
void  register_module_number(unsigned int module_number)
{
	if(module_number > 0)
		module_maxnumber = module_number;
}

int __duster_call_module_post_set_handler(struct applet_info *di, int flags, dc_args_t *dca)
{
	/* This is the module applet info structure */
	if (di->di_handler_post_set_func) {
		di->di_handler_post_set_func(flags, dca);
	}
#if 0
    else if (di->di_handler_post_set_cmd) {
		duster_call_module_run_cmd(di->di_handler_post_set_cmd, flags, dca->dc_opaque);
		if (dca->dc_opaque) {
			/* If there is a pre_set but no post_set, we could lose this
			 * allocation */
			duster_free(dca->dc_opaque);
			dca->dc_opaque = NULL;
		}
	}
#endif
	/* Else do nothing */
	return 0;
}

int __duster_call_module_pre_set_handler(struct applet_info *di, int flags, dc_args_t *dca)
{
	/* This is the module applet info structure */
	if (di->di_handler_pre_set_func) {
		di->di_handler_pre_set_func(flags, dca);
	}
	#if 0
	else if (di->di_handler_pre_set_cmd) {
		char *ptr = NULL;
		/* For preset handler if opaque is set, create a temporary file
		 * and send it
		 */
		ptr = strdup("/tmp/duster.XXXXXX");
		if (mkstemp(ptr) < 0) {
			return -1;
		}
		dca->dc_opaque = (void *)ptr;
		duster_call_module_run_cmd(di->di_handler_pre_set_cmd, flags, ptr);
	}
	#endif 
	/* Else do nothing */
	return 0;
}

int __duster_call_module_pre_get_handler(struct applet_info *di, int flags, dc_args_t *dca)
{
	/* This is the module applet info structure */
        if (di->di_handler_pre_get_func) {
                di->di_handler_pre_get_func(flags, dca);
        }
#if 0
        else if (di->di_handler_pre_get_cmd) {
		duster_call_module_run_cmd(di->di_handler_pre_get_cmd, flags, NULL);
	}
#endif 
	return 0;
}

int __duster_call_module_post_get_handler(struct applet_info *di, int flags, dc_args_t *dca)
{
	/* This is the module applet info structure */
	duster_log("=== LOG10: 准备调用模块函数 ===");
        if (di->di_handler_post_get_func) {
                duster_log("=== LOG10: 调用 %s 的post_get函数 ===", di->di_modname);
                return di->di_handler_post_get_func(flags, dca);
        } else {
                duster_log("=== LOG10: 模块 %s 没有post_get函数 ===", di->di_modname);
        }
#if 0
        else if (di->di_handler_post_get_cmd) {
		duster_call_module_run_cmd(di->di_handler_post_get_cmd, flags, NULL);
	}
#endif
	return 0;
}

int __duster_call_module_validate_handler(struct applet_info *di, char *variable_name, dc_args_t *dca)
{
	/* This is the module applet info structure */
        if (di->di_handler_validate_func) {
                return di->di_handler_validate_func(variable_name, dca);
	}
	return 0;
}

int duster_call_module_post_set_handler(char *modname, int flags, dc_args_t *dca)
{
	int i, ret = 0;                                                             
	struct applet_info *module_applets = module_applet_array;                                     
	int applets_no = module_maxnumber;                                                         
                                                                                
	for (i = 0; i < applets_no; i++) {                                      
		if ( !strcmp(modname, module_applets[i].di_modname)) {          
			ret = __duster_call_module_post_set_handler(&module_applets[i], flags, dca);   
			break;                                                  
		}                                                               
	}                                                                       
                                                                                
	return ret; 
}

int duster_call_module_pre_set_handler(char *modname, int flags, dc_args_t *dca)
{
        int i, ret = 0;                                                             
        struct applet_info *module_applets = module_applet_array;                                     
        int applets_no = module_maxnumber;                                                         
                                                                                     
        for (i = 0; i < applets_no; i++) {                                      
            if ( !strcmp(modname, module_applets[i].di_modname)) {          
                ret = __duster_call_module_pre_set_handler(&module_applets[i], flags, dca);   
                break;                                                  
            }                                                               
        }                                                                       
                                                                                    
        return ret; 
}

int duster_call_module_pre_get_handler(char *modname, int flags, dc_args_t *dca)
{
            int i, ret = 0;                                                             
            struct applet_info *module_applets = module_applet_array;                                     
            int applets_no = module_maxnumber;                                                         
                                                                                        
            for (i = 0; i < applets_no; i++) {                                      
                if ( !strcmp(modname, module_applets[i].di_modname)) {          
                    ret = __duster_call_module_pre_get_handler(&module_applets[i], flags, dca);   
                    break;                                                  
                }                                                               
            }                                                                       
                                                                                        
            return ret; 
}

int duster_call_module_post_get_handler(char *modname, int flags, dc_args_t *dca)
{
    int i, ret = 0;
    struct applet_info *module_applets = module_applet_array;
    int applets_no = module_maxnumber;

    duster_log("=== LOG9: 查找模块: %s, 搜索范围: 0-%d ===", modname, applets_no-1);

    for (i = 0; i < applets_no; i++) {
        duster_log("=== LOG9: 检查模块[%d]: %s ===", i, module_applets[i].di_modname);
        if ( !strcmp(modname, module_applets[i].di_modname)) {
            duster_log("=== LOG9: 找到匹配模块[%d]: %s ===", i, modname);
            ret = __duster_call_module_post_get_handler(&module_applets[i], flags, dca);
            duster_log("=== LOG9: 模块函数调用返回: %d ===", ret);
            break;
        }
    }

    if(i >= applets_no) {
        duster_log("=== LOG9: 未找到模块: %s ===", modname);
    }

    return ret;
}

int duster_call_module_validate_handler(char *modname, char *variable_name, dc_args_t *dca)
{
    int i, ret = 0;                                                             
    struct applet_info *module_applets = module_applet_array;                                     
    int applets_no = module_maxnumber;                                                         
                                                                                
    for (i = 0; i < applets_no; i++) {                                      
        if ( !strcmp(modname, module_applets[i].di_modname)) {          
            ret = __duster_call_module_validate_handler(&module_applets[i], variable_name, dca);   
            break;                                                  
        }                                                               
    }                                                                       
                                                                                
    return ret;    
}

int __duster_call_module_upgrade_handler(struct applet_info *di, int modver)
{
	if (di->di_handler_upgrades) {
		return di->di_handler_upgrades(modver);
	}
	return 0;
}


int __duster_call_module_dynamic_handler(struct applet_info *di, int getset, int flags, dc_args_t *dca)
{
	if (di->di_handler_dynamic) {
		return di->di_handler_dynamic(getset, flags, dca);
	}
	return 0;
}

int duster_call_module_dynamic_handler(char *modname, int getset, int flags, dc_args_t *dca)
{
    int i, ret = 0;                                                             
    struct applet_info *module_applets = module_applet_array;                                     
    int applets_no = module_maxnumber;                                                         
                                                                                
    for (i = 0; i < applets_no; i++) {                                      
        if ( !strcmp(modname, module_applets[i].di_modname)) {          
            ret = __duster_call_module_dynamic_handler(&module_applets[i], getset, flags, dca);   
            break;                                                  
        }                                                               
    }                                                                       
                                                                                
    return ret; 

}

void duster_call_module_get_delimiters(char *modname, char *fd, char *rd)
{
	int i;
	struct applet_info *module_applets = module_applet_array;
	int applets_no = module_maxnumber;

	*fd = '\0';
	*rd = '\0';
	//module_applets = duster_get_module_applets(&applets_no);

	for (i = 0; i < applets_no; i++) {
		if ( !strcmp(modname, module_applets[i].di_modname)) {
			*rd = module_applets[i].di_rd;
			*fd = module_applets[i].di_fd;
			break;
		}
	}
	if (*fd == '\0') {
		/* : is the default field delimiter */
		*fd = '%';
	}

	if (*rd == '\0') {
		/*  (space) is the default record delimiter */
		*rd = '^';
	}
}
