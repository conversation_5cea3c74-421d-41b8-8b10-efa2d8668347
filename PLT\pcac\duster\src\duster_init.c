/* -*- linux-c -*- */
/*******************************************************************************
*               Copyright 2009, Marvell Technology Group Ltd.
*
* THIS CODE CONTAINS CONFIDENTIAL INFORMATION OF MARVELL. NO RIGHTS ARE GRANTED
* HEREIN UNDER ANY PATENT, MASK WORK RIGHT OR COPYRIGHT OF MARVELL OR ANY THIRD
* PARTY. MARVELL RESERVES THE RIGHT AT ITS SOLE DISCRETION TO REQUEST THAT THIS
* CODE BE IMMEDIATELY RETURNED TO MARVELL. THIS CODE IS PROVIDED "AS IS".
* MARVELL MAKES NO WARRANTIES, EXPRESS, IMPLIED OR OTHERWISE, REGARDING ITS
* ACCURACY, COMPLETENESS OR PERFORMANCE. MARVELL COMPRISES MARVELL TECHN<PERSON>OGY
* GROUP LTD. (MTGL) AND ITS SUBSIDIARIES, MARVELL INTERNATIONAL LTD. (MIL),
* MARVELL TECHNOLOGY, INC. (MTI), MARVELL SEMICONDUCTOR, INC. (MSI), MARVELL
* ASIA PTE LTD. (MAPL), MARVELL JAPAN K.K. (MJKK), GALILEO TECHNOLOGY LTD. (GTL)
* GALILEO TECHNOLOGY, INC. (GTI) AND RADLAN Computer Communications, LTD.
********************************************************************************
*/
#include <stdio.h>
#include <stdlib.h>
#include <psm.h>
#include <duster_applets.h>
#include "module_func.h"
#include "psm_wrapper.h"
#include "Dialer_Task.h"
#include "MRD.h"
//#include "telprod.h"
//#include "OLED_Task.h"
#include "platform.h"
//#include "internal_ver.h"
#include "wan_applet.h"
#include "rdiskfsys.h"
#include "platform.h"
#include "MRD_structure.h"
#include "ReliableData.h"
#include "FlashPartition.h"
#include "cgi.h"
#include "phonebook_applet.h"
#include "connect_management.h"
#include "statistics_applet.h"
#include "lwip_api.h"
#ifdef WEBUI_PB_SUPPORT
#include "phonebook_applet.h"
#endif

OSASemaRef DialerReadySema = NULL;
OSASemaRef WanCellularSema = NULL;
extern OSATimerRef TimerDeleteDusterInitTask;

extern OSASemaRef WanCellularSema;
extern OSMsgQRef gCommonATMsgQ;
extern OSMsgQRef gSendATMsgQ;
OSMsgQRef gSavePSMMSGQ=NULL;
long rx_rate=0 ,tx_rate=0 ;
static utlTimerId_T DS_timer_id = 0;
static utlRelativeTime_T DS_delay = { 1, 0 };
#ifdef SN_PASSWORD_SUPPORT
char DEFAULT_END_USERNAME[MAX_NAME_SIZE];
#endif
int WaitCfun0Flag = 1;

OSASemaRef SentATSema;
extern OSMsgQRef gWlanIndMSGQ;
extern OSMsgQRef gDMRegisterRespMSGQ;
extern OSMsgQRef gVoipRespMSGQ;//roy add for voip
extern OSMsgQRef gsATPMsgQ[NUM_OF_TEL_ATP];
extern psm_handle_t *g_handle[TOTAL_PSMFILE_NUM];
extern OSASemaRef DialerReadySema;
extern OSASemaRef WebSMSSema;
extern OSASemaRef SimSmsInitSema;

#ifdef WEBUI_PB_SUPPORT
extern OSASemaRef PhoneBookSema ;
#endif
extern GlobalParameter_Duster2Dialer gUI2DialerPara;
extern GlobalParameter_Dialer2Duster gDialer2UIPara;
extern char KeyFile[82];
extern GlobalParameter_Duster2Dialer gUI2DialerPara;
extern GlobalParameter_Dialer2Duster gDialer2UIPara;
extern UINT8 ATCmdSvrRdy;
extern char IMSI[16];
extern BOOL SelectNWFlag;
extern UINT32 WIFI_SLEEP_TIME;
extern UINT32 disconnectnetwork;
extern char SWVersionS[33];
extern UINT32 g_set_atcops_done;
//extern char * g_backup_psm_buf;
extern OSMsgQRef gATMsgQ;
extern s_switch_image_ctx switch_ctx;
extern UINT8 gMncLen;

char *g_backup_psm_buf = NULL;

int duster_init_wan(void);

extern void (*Dhcp_cb)(UINT32);
extern unsigned char is_usim_card(void);
extern void dialer_Queue_init(void);
extern void SavePsmData(void);
extern void update_WLAN_client_Task(void *argc);
extern void SaveTDStatisticsTask(void *argc);
extern int user_management_init(void);
extern void DeleteDusterInitTaskByTimer(UINT32 id);
#ifdef WEBUI_WEBDAV_SUPPORT
extern int webdav_management_init(void);
#endif
extern int ACAT_ToSD_Notify_WebUI(void);
extern int DualBand_Support_Notify_WebUI(void);
extern void UpdateWlanClientInfoTask(void *argc);
extern void CheckUpgradeReset(void);
extern void WAPI_Support_Notify_WebUI(void);
extern 	s_WlanStatistics *load_wlan_client_info_alloc(void);
extern void FastEventHandlerTask(void *argc);
extern void redirect_init(void);
extern 	int wlan_duster_config(int Action);
extern void Wlan_AfterConfig(void);
/*added by shoujunl 131030 start*/
char *GetSSGInternalVer(void);
char *GetSSGCompileTime(void);
/*added by shoujunl 131030 end*/
extern void readFBFFlash(BOOL flash4bitFlag);
extern UINT32 is_upgrade_by_obm(UINT32 *get_method);
extern UINT32 notify_webui_upgrade_status(int flag);
extern UINT32 check_obm_upgrade_success_flag(void);
extern void set_upgrade_success_flag(UINT32 flag);
extern char * dump_backup_psm_into_buf_alloc(void);
extern void loadDetailLog(void);
extern int WIFI_20M_Notify_WebUI(void);
extern void clear_obm_upgrade_success_flag(void);
extern void SetCfunValue(int value);
extern unsigned int get_subnet_mask_psm(void);
void Read_Module_Debug_level(void);
extern void set_mifi_dns_name(char* str);
extern void displaySwitch(void);
extern int duster_auto_select_netwrok(int at_channel);
extern int wan_sim_check(int Action, UINT32 AT_channel);
extern void wlan_default_setting(void);
extern int process_ussd_action(enum e_ui_ussd_action action, int at_channel);
extern BOOL is_wifi6_type(void);
extern int GetWiFiType(void);


/*set whether webserver allow only one user logining at the same time*/
//extern void set_single_user_login_setting(int setting);

struct applet_info module_applet_array[module_maxnum]=
{
	{
		.di_modname = "lan",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_get_func = lan_get,
		.di_handler_pre_set_func = lan_pre_set,
		.di_handler_post_set_func = lan_post_set,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
#ifdef WIFI_SUPPORT
	{
		.di_modname = "wlan_settings",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_func = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = wlan_settings_post_set,
		.di_handler_post_get_func = wlan_get,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "wlan_security",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_get_func = wlan_security_post_get,
		.di_handler_pre_set_func = wlan_security_pre_set,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = wlan_security_post_set,
		.di_handler_post_set_cmd =NULL,
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "wlan_mac_filters",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_get_func = wlan_mac_filter_post_get,
		.di_handler_pre_set_func = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = wlan_mac_filter_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
		.di_fd = ':',
		.di_rd = '^',
	},
#endif
	{
		.di_modname = "wan",
		.di_handler_post_get_func = wan_get,
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_get_func = wan_pre_get,
		.di_handler_pre_set_func = wan_pre_set,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = wan_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
		.di_fd = '%',
		.di_rd = '^',
	},
	{
		.di_modname = "management",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_pre_set_func = user_management_pre_set,
		.di_handler_post_set_func = user_management_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
		.di_fd = '%',
		.di_rd = '^',
	},
#ifdef QUECTEL_PROJECT_CUST
// Begin Add by ycc, Change sim card logic
	{
		.di_modname = "sim_manage",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_validate_func = NULL,
		.di_handler_pre_set_func = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = sim_manage_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_pre_get_func = NULL,
		.di_handler_pre_get_cmd = NULL,
		.di_handler_post_get_func = sim_manage_post_get,
		.di_handler_post_get_cmd = NULL,
		.di_handler_dynamic = NULL,
		.di_fd = '%',
		.di_rd = '^',
	},
// End Add by ycc, Change sim card logic
#endif
#ifdef WEBUI_WEBDAV_SUPPORT
	{
		.di_modname = "webdav_user_management",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_pre_set_func = webdav_user_management_pre_set,
		.di_handler_post_set_func = webdav_user_management_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
		.di_fd = '%',
		.di_rd = '^',
	},

	{
		.di_modname = "webdav_shared_management",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_pre_set_func = webdav_shared_management_pre_set,
		.di_handler_post_set_func = webdav_shared_management_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
		.di_fd = '%',
		.di_rd = '^',
	},
#endif
	{
		.di_modname = "device_management",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_get_func = get_device_list,
		.di_handler_post_get_func = get_connected_hosts,
		.di_handler_pre_get_cmd = NULL,
		.di_handler_post_set_func = device_management_post_set,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
	{
		.di_modname = "log_management",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_get_func = log_management_pre_get,
		.di_handler_post_set_func = log_management_post_set,
		.di_handler_dynamic = NULL,
		.di_fd = '%',
		.di_rd = '^',
	},
	{
		.di_modname = "custom_fw",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = custom_ip_filter_post_set,
		.di_handler_post_set_cmd = "/etc/init.d/custom_fw_post_set.sh",
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = ' ',
	},
	{
		.di_modname = "domain_name_filter",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = domain_name_filter_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
	{
		.di_modname = "detailed_log",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = detailed_list_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_post_get_func = detailed_list_post_get,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
	{
		.di_modname = "port_forwarding",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_get_func = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = custom_port_forwarding_post_set,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
	{
		.di_modname = "time_setting",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_get_func = NULL,
		.di_handler_pre_get_func = time_get,
		.di_handler_post_set_func = post_time_setting,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "statistics",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_func = NULL,
		.di_handler_post_get_cmd = NULL,
		.di_handler_pre_get_func = get_stat_used_with_cur_month,
		.di_handler_post_get_func = stat_get,
		.di_handler_post_get_cmd = NULL,
		.di_handler_post_set_func = reset_statistics,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
	{
		.di_modname = "restore_defaults",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_get_cmd = NULL,
		.di_handler_post_get_func = restore_default,
		.di_handler_post_get_cmd = NULL,
		.di_handler_post_set_func = NULL,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "reboot",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_get_cmd = NULL,
		.di_handler_pre_get_func = NULL,
		.di_handler_post_get_func = reset_router,
		.di_handler_post_get_cmd = NULL,
		.di_handler_post_set_func = NULL,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "power_off",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_get_cmd = NULL,
		.di_handler_pre_get_func = NULL,
		.di_handler_post_get_func = power_off,
		.di_handler_post_get_cmd = NULL,
		.di_handler_post_set_func = NULL,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "sd_info",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_get_func = NULL,
		.di_handler_post_get_func = sd_info_post_get,
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "ntp_setting",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_set_func = post_ntp_setting,
		.di_handler_post_get_func = SyncNTPFlagGet,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "sysinfo",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_set_func = post_version_number,
		.di_handler_post_set_cmd = NULL,
#ifdef QUECTEL_PROJECT_CUST
		.di_handler_post_get_func = sysinfo_post_get,
#endif
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "file_sharing",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_set_func = NULL,
		.di_handler_post_set_cmd = "/etc/init.d/file_sharing_post_set.sh",
		.di_handler_dynamic = NULL,
	},
	{
		.di_modname = "upgrade",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_set_func = sd_upgrade_post_set,
		.di_handler_pre_get_func = sd_upgrade_post_get,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
	{
		.di_modname = "webui_upgrade",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_post_set_func = NULL,
		.di_handler_post_get_func = upgrade_progress_post_get,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},

	{
		.di_modname = "pin_puk",
		.di_handler_post_get_func = pin_puk_post_get,
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_get_func = NULL,
		.di_handler_pre_set_func = NULL,
		.di_handler_pre_set_cmd = NULL,
		.di_handler_post_set_func = pin_puk_post_set,
		.di_handler_post_set_cmd = NULL,
		.di_handler_dynamic = NULL,
	},
#ifdef WEBUI_PB_SUPPORT
	{
		.di_modname = "phonebook",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_func = NULL,
		.di_handler_post_get_func = PB_post_get,
		.di_handler_pre_get_cmd = NULL,
		.di_handler_post_set_func = PB_post_set,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
#endif
#ifdef SMS_ENABLE
	{
		.di_modname = "message",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_func = NULL,
		.di_handler_post_get_func = SMS_post_get,
		.di_handler_pre_get_cmd = NULL,
		.di_handler_post_set_func = SMS_post_set,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
#endif

	{
		.di_modname = "port_filter",
		.di_version = 1,
		.di_handler_upgrades = NULL,
		.di_handler_pre_set_func = NULL,
		.di_handler_post_get_func = NULL,
		.di_handler_pre_get_cmd = NULL,
		.di_handler_post_set_func = port_filter_post_set,
		.di_handler_dynamic = NULL,
		.di_fd = '#',
		.di_rd = '^',
	},
#ifdef DUSTER_USSD_SUPPORT
	{
	.di_modname = "ussd",
	.di_version = 1,
	.di_handler_upgrades = NULL,
	.di_handler_pre_set_func = NULL,
	.di_handler_post_get_func = ussd_post_get,
	.di_handler_pre_get_cmd = NULL,
	.di_handler_post_set_func = ussd_post_set,
	.di_handler_dynamic = NULL,
	.di_fd = '#',
	.di_rd = '^',
	},
#endif
};

extern void duster_Queue_init(void);


void set_fxn_project_type(void)
{
	#if defined(FOXCONN_MIFI1)
		psm_set_wrapper("sysinfo", NULL, "project_type","FOXCONN_MIFI1");
	#elif defined(FOXCONN_MIFI2)
		psm_set_wrapper("sysinfo", NULL, "project_type","FOXCONN_MIFI2");
	#else
		psm_set_wrapper("sysinfo", NULL, "project_type","");
	#endif

	return;
}
void wan_psm_item_init(void)
{
	char *str = NULL;

	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "manual_network_start");
	if((str != NULL) && (strncmp(str,"",1)))
	{
		Duster_module_Printf(1,"%s: manual_network_start is %d",__FUNCTION__, str[0]-'0');
		if((str[0]-'0') == 0)
			SelectNWFlag = TRUE;            //auto select NW
		else if((str[0]-'0') == 1)
			SelectNWFlag = FALSE;           //manual select NW
		else
			Duster_module_Printf(1,"%s:wrong value in manual_network_start ", __FUNCTION__);
	}

	if(str)
	{
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "disconnectnetwork");
	if(str != NULL)
	{
		disconnectnetwork = atoi(str);
		Duster_module_Printf(1,"%s:disconnectnetwork is %d", __FUNCTION__,disconnectnetwork);
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "auto_switch");
	if(str != NULL)
	{
	       set_auto_switch(atoi(str), FALSE);
		Duster_module_Printf(1,"%s: AutoSwitchFlag is %s %d",__FUNCTION__,str, switch_ctx.auto_switch);
		duster_free(str);
		str = NULL;
    }

    str = psm_get_wrapper(PSM_MOD_WAN, NULL, "first_Instant_Switch_Tick");
	if(str != NULL)
	{
	       switch_ctx.last_switch_tick = atoi(str);
		Duster_module_Printf(1,"%s: firstInstantSwitchTick is %s %d",__FUNCTION__,str, switch_ctx.last_switch_tick);
		duster_free(str);
		str = NULL;
    }

	//clear up pdp context list when system starts
	psm_set_wrapper(PSM_MOD_WAN, "cellular", "pdp_context_list","");

	//enable STK
	psm_set_wrapper(PSM_MOD_WAN, NULL, "stk_enable","1");

	/*disable bgscan time on NezhaC*/
	if (PlatformIsNezhaC())
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "bgscan_time_action", "0");

	return;
}

#ifdef  SN_PASSWORD_SUPPORT
void set_end_username(char *name)
{
	memset(DEFAULT_END_USERNAME, 0, MAX_NAME_SIZE);
	strncpy(DEFAULT_END_USERNAME, name , MAX_NAME_SIZE - 1);
}

char *get_end_username()
{
	return DEFAULT_END_USERNAME;
}
#endif

void ntp_status_init()
{
#ifdef NTP
	char *str = psm_get_wrapper("time_setting", NULL, "ntp_status");

	if (!str || !strncmp(str, "", 1))
	{
		psm_set_wrapper("time_setting", NULL, "ntp_status", "enable");
	}

	if (str)
	{
		duster_free(str);
	}
#endif
}

int get_duster_ready_flag(void)
{
    return  (int)gUI2DialerPara.dusterReadyFlag;
}

#define WEB_FILE_TYPE               0xCAFE1000
extern BOOL isRdiskfsysInit;
extern BOOL RDSaveFilesToFS(RD_TCHAR *pFile_name,void *buff, size_t size,size_t element_size);

void RDWebFile_Init(void)
{
    BOOL retVal;
    UINT8 exist = FALSE;
    RD_ENTRY entry = {0};
    RD_BUFFER_HEADER header = {0};
    unsigned int flashaddress = 0;
    FlashLayoutConfInfo *pFlashLayout = GetFlashLayoutConfig();

    if(!PlatformWebIsEnable())
    {
        isRdiskfsysInit = TRUE;
        return;
    }

    flashaddress = pFlashLayout->WEBStartAddress;
	duster_log("%s, web start address 0x%x, web end address 0x%x", __FUNCTION__, flashaddress, pFlashLayout->WEBEndAddress);

    Rdisk_FlashRead(flashaddress, (unsigned char *)(&header), sizeof(RD_BUFFER_HEADER));

    /* check stamp in case of failure print only if we at phase2 after diag is ready */
    /* if we did check sum verification don't do it again*/
    if (header.validBufferStamp != VALID_BUF_STMP)
    {
        duster_log("WEB file: Stamp:%lx, Version =%lx", header.validBufferStamp, header.version);
		isRdiskfsysInit = TRUE;
        return;
    }

    flashaddress += sizeof(RD_BUFFER_HEADER);

    do
    {
        UINT32 entry_size = 0;
        UINT8  pad_size = 0;

        Rdisk_FlashRead(flashaddress, (unsigned char *)(&entry), sizeof(RD_ENTRY));

        flashaddress = flashaddress + sizeof(RD_ENTRY)- 4;
        entry.pEntryData = (UINT32 *)flashaddress;

        entry_size = (entry.fileAndPadSize & 0x00FFFFFF);
        pad_size = (UINT8)(((entry.fileAndPadSize & 0xFF000000) >> 24));

        /* move the pointer to next entry - the entry data was copied above ->entry*/
        flashaddress += entry_size;

        switch(entry.entryType)
        {
            case WEB_FILE_TYPE:
            {
                UINT32 file_size;

                file_size = entry_size - pad_size;

				//duster_log("%s: flashaddress 0x%x, file name %s, file size %d", __func__, flashaddress, entry.file_name, file_size);
                /* according to desgin when ever we will reach here we will create new files and override exsiting ones*/
                if(file_size != 0)
                {
                    if(strncmp(entry.file_name, "www\\data", 8)==0)
                    {
                        char *file = (char *)malloc(file_size);
                        ASSERT(file != NULL);

                        Rdisk_FlashRead((unsigned int)entry.pEntryData, (unsigned char *)file, file_size);
                        retVal = RDSaveFilesToFS(entry.file_name, file, file_size, sizeof(UINT8));
                        ASSERT(retVal == TRUE);

                        free(file);
                    }
                    else
                    {
                        retVal = Rdisk_addto_filelist(entry.file_name, (UINT32)entry.pEntryData, file_size);
                        ASSERT(retVal == TRUE);
                    }
                }

                break;
            }

            default:
            {
                duster_log("%s: end entry type:%lx", __func__, entry.entryType);
                break;
            }
        }
    }
    while((entry.entryType != (UINT32)END_BUF_STMP) && (flashaddress < pFlashLayout->WEBEndAddress));

    isRdiskfsysInit = TRUE;

    duster_log("%s flashaddress 0x%x end", __FUNCTION__, flashaddress);
    return;
}

#ifdef WIFI_SUPPORT
#define DUSTERIN_WIFI_TASK_STACK_SIZE  (8*1024)
static OSTaskRef gDusterWiFiInitTask = NULL;
static unsigned char *gDusterWiFiInitTaskStack = NULL;
OSATimerRef TimerDeleteDusterWiFiInitTask = NULL;

static void DeleteDusterWiFiInitTaskByTimer(UINT32 id)
{
    OSA_STATUS status;

    if (gDusterWiFiInitTask != NULL)
    {
        status = OSATaskDelete(gDusterWiFiInitTask);
        ASSERT(status == OS_SUCCESS);

        gDusterWiFiInitTask = NULL;

        if (gDusterWiFiInitTaskStack != NULL)
        {
            free(gDusterWiFiInitTaskStack);
            gDusterWiFiInitTaskStack = NULL;
        }
    }

    OSATimerDelete(TimerDeleteDusterWiFiInitTask);
}

static void duster_wifi_init_task(void *argc)
{
	char *str = NULL;
	char tmp_buf[20];
	int ret;
	
	str=psm_get_wrapper(PSM_MOD_WLAN_SETTING,NULL,"wifi_sleep_time");
	if(str)
	{
		WIFI_SLEEP_TIME=atoi(str);
		duster_log("%s: WIFI_SLEEP_TIME %d", __FUNCTION__, WIFI_SLEEP_TIME);
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WLAN_SETTING,NULL,"wifi_sleep_action");
	if (str)
	{
		if (2 == atoi(str))
		{
			duster_log("%s: should enable wlan", __FUNCTION__);
			psm_set_wrapper(PSM_MOD_WLAN_SETTING,NULL,"wlan_enable","1");
		}

		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WLAN_SETTING,NULL,"net_mode_action");
	if (str == NULL || (1 != atoi(str) && is_wifi6_type()))
	{
		duster_log("%s: set wifi6 default mode to ax", __FUNCTION__);
		psm_set_wrapper(PSM_MOD_WLAN_SETTING,NULL,"net_mode","4");
	}

	if (str) 
	{
		duster_free(str);
		str = NULL;
	}
	
//add by ycc, modify max_clients
	// if (is_wifi6_type())
	// {
	// 	snprintf(tmp_buf, sizeof(tmp_buf), "%d", MAX_WLAN_CLIENT_NUMBER);
	// 	psm_set_wrapper(PSM_MOD_WLAN_SETTING,NULL,"allowed_max_clients",tmp_buf);

	// 	str = psm_get_wrapper(PSM_MOD_WLAN_SETTING,NULL,"max_clients_action");
	// 	if (str == NULL || 1 != atoi(str))
	// 	{
	// 		duster_log("%s: set wifi6 default max_clients to 32", __FUNCTION__);
	// 		psm_set_wrapper(PSM_MOD_WLAN_SETTING,NULL,"max_clients","32");
	// 	}
	// 	if (str)
	// 	{
	// 		duster_free(str);
	// 		str = NULL; 
	// 	}
	// }
	// else
	// 	psm_set_wrapper(PSM_MOD_WLAN_SETTING,NULL,"allowed_max_clients","10");
// End Add by ycc, modify max_clients

	str = psm_get_wrapper(PSM_MOD_WLAN_SETTING,NULL,"bandwidth_action");
	if ((str == NULL || 1 != atoi(str)) && GetWiFiType() == 6)
	{
		duster_log("%s: set wifi6 default bandwidth to 0", __FUNCTION__);
		psm_set_wrapper(PSM_MOD_WLAN_SETTING,NULL,"bandwidth","0");
	}

	if (str)
	{
		duster_free(str);
		str = NULL; 
	}

	psm_set_wrapper(PSM_MOD_WLAN_SECURITY, NULL, "ssid_suffix_flag", "0");

	// set 20M only flag to WebUI
	WIFI_20M_Notify_WebUI();
	ret = wlan_duster_config(DUSTER_CONFIG_START);
	if (ret == 0)
		Wlan_AfterConfig();

	if(!TimerDeleteDusterWiFiInitTask)
    {
        OSATimerCreate(&TimerDeleteDusterWiFiInitTask);
    }
    OSATimerStart(TimerDeleteDusterWiFiInitTask, 200, 0, DeleteDusterWiFiInitTaskByTimer, 0);
}

static void duster_wifi_init(void)
{
	OSA_STATUS osaStatus;
	
    if(!gDusterWiFiInitTaskStack)
    {
        gDusterWiFiInitTaskStack = malloc(DUSTERIN_WIFI_TASK_STACK_SIZE);
    }
	osaStatus  = OSATaskCreate(&gDusterWiFiInitTask,
                           gDusterWiFiInitTaskStack,
                           DUSTERIN_WIFI_TASK_STACK_SIZE,
                           128,
                           (char *)"DusterWiFiInitTask" ,
                           duster_wifi_init_task,
                           (void *)0);

}
#endif

void duster_task_init(void *argc)
{
	int i, modver;
	struct applet_info *module_applets;
	int applets_no;
	char *str=NULL;
	int dialmode=0;
	dc_args_t dca = { };
	UINT32 upgrade_method = 0;
	char tmp_buf[20];

	RDWebFile_Init();
#ifdef QUECTEL_PROJECT_CUST
    char new_str[20] = {0};
    char *imei_str = NULL;
#endif

	if (PlatformGetProjectType() == NEZHA_PCIE_DGLE || bspGetBoardType() == TIGX_MIFI || PlatformWebIsEnable() == FALSE || InProduction_Mode())
	{
		ntp_status_init();
		duster_init_wan();
		loadStatisticsData();
#if defined(PPP_ENABLE)
		send_cgdcont(AT_WEBUI_CHANNEL, 1, TRUE);
#endif
		goto toEnd;
	}

	CheckUpgradeReset();

    readFBFFlash(TRUE);

#ifdef SUPPORT_SD_UPGRADE
    if(is_upgrade_by_obm(&upgrade_method))
    {
   		if(upgrade_method == 2)
			notify_webui_upgrade_status(2);//failed

    }
	else
	{
		if(check_obm_upgrade_success_flag())
    	{
    		if(upgrade_method == 2)
    		{
				notify_webui_upgrade_status(1);//success
    		}
			set_upgrade_success_flag(1);
			g_backup_psm_buf = dump_backup_psm_into_buf_alloc();
    	}
		else
			notify_webui_upgrade_status(0);// No upgrade
    }
#endif

	loadDetailLog();
	loadStatisticsData();

	//   module_level[Module_Duster] = TRACE_NOTICE;
	//   module_level[Module_Dial] = TRACE_WARNING;
	gUI2DialerPara.dusterReadyFlag = 1;


	applets_no = module_maxnum;

	//module_applets = duster_get_module_applets(&applets_no);
	module_applets = module_applet_array;

	register_module_number(module_maxnum);

#ifdef  SN_PASSWORD_SUPPORT
	set_end_username("SERIAL");
#endif
	/* Setup variables that are not defined to default values */
	duster_parser_nucleus("default", "www\\defaults\\admin.xml", NULL);
	// set user managment multi account flag
	user_management_init();
#ifdef WEBUI_WEBDAV_SUPPORT
	duster_parser_nucleus("default", "www\\defaults\\webdav_management.xml", NULL);
	webdav_management_init();
#endif
	set_fxn_project_type();

	duster_parser_nucleus("default", "www\\defaults\\lan.xml", NULL);
	// set dns name action to 1
	psm_set_wrapper(PSM_MOD_LAN, NULL, "dns_name_action","1");
	lan_post_set(DUSTER_CONFIG_START,	NULL);
	duster_parser_nucleus("default", "www\\defaults\\device_management.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\wlan_mac_filters.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\wlan_primary_security.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\wlan_settings.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\wlan_wps_client.xml", NULL);

#ifdef WIFI_SUPPORT
	duster_wifi_init();
#endif
	UIRefresh_Throughput_Change();

	/* Setup variables that are not defined to default values */
	duster_parser_nucleus("default", "www\\defaults\\wan.xml", NULL);
	//should re-init wan relate psm items here.
	wan_psm_item_init();
#ifdef QUECTEL_PROJECT_CUST
	duster_parser_nucleus("default", "www\\defaults\\sim_manage.xml", NULL);
#endif
	duster_parser_nucleus("default", "www\\defaults\\domain_name_filter.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\custom_fw.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\port_forwarding.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\locale.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\theme.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\pin_puk.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\statistics.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\time_setting.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\message.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\detailed_log.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\phonebook.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\port_filter.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\upgrade_firmware.xml", NULL);
	duster_parser_nucleus("default", "www\\defaults\\Engineer_parameter.xml", NULL);

    gUI2DialerPara.dusterReadyFlag = 3;
	clear_obm_upgrade_success_flag();
	if(g_backup_psm_buf)
	{
		// should free the buf, if it is exist
		duster_free(g_backup_psm_buf);
		g_backup_psm_buf = NULL;
	}
#if LWIP_REDIRECT
	redirect_init();
#endif
	CheckUpgradeReset();
	/* Handle module upgrades */
	psm_open__(PSM_MOD_DEFAULT,NULL);
	str = psm_get_wrapper("wan", NULL, "connect_mode");
    Duster_module_Printf(1,"%s: connect_mode is %s",__FUNCTION__, str);
	if(str)
	{
		dialmode = str[0]-'0';
		duster_free(str);
		str = NULL;
	}
	if(dialmode == 0)//auto dial
	{
		CPUartLogPrintf("auto dial when power on");
		psm_set_wrapper("wan", NULL, "connect_disconnect", "cellular");
	}

	psm_set_wrapper(PSM_MOD_WAN, "cellular", "mannual_network_list","");/*clear last search Network Result*/
	psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_select_done","");

	str = psm_get_wrapper("sysinfo", NULL, "version_num");
	if(str&&strncmp(str,	"",	1))
	{

		Duster_module_Printf(1,"duster_task_init:SWVersionS is %s",SWVersionS);
		memset(SWVersionS,0,33);
		if(strlen(str)<=32)
			memcpy(SWVersionS,str,strlen(str));
		else
			memcpy(SWVersionS,str,32);

		Duster_module_Printf(1,"duster_task_init:SWVersionS is %s",SWVersionS);
	}

	if (str)
	{
		duster_free(str);
		str = NULL;
	}

	psm_set_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_auto_dial_action","1");
	// set ACAT to SD support or not flag
	ACAT_ToSD_Notify_WebUI();
	// set WIFI 5G Ssupport or not flag
	#ifdef WIFI_SUPPORT
	DualBand_Support_Notify_WebUI();
	// set WAPI support or not falg to WebUI
	WAPI_Support_Notify_WebUI();
	#endif

#if 0
	/*added by shoujunl 131030 start*/
	char *ssg_ver = NULL;
	char *ssg_complie_time = NULL;
	ssg_ver = GetSSGInternalVer();
	if(ssg_ver)
	{
		Duster_module_Printf(1,	"%s ssg_ver is %s",	__FUNCTION__,	ssg_ver);
		psm_set_wrapper("sysinfo", NULL, "ssg_version",	ssg_ver);
	}

	ssg_complie_time = GetSSGCompileTime();
	if(ssg_complie_time)
	{
		Duster_module_Printf(1,	"%s ssg_compile_time is %s",	__FUNCTION__,	ssg_complie_time);
		psm_set_wrapper("sysinfo", NULL, "ssg_compile_time",	ssg_complie_time);
	}
	/*added by shoujunl 131030 End*/
#endif

	for (i = 0; i < applets_no; i++)
	{
	    if (module_applets[i].di_modname)
		    duster_Printf("%s: start %s" ,__FUNCTION__, module_applets[i].di_modname);

        psm_lock_low();
		/* Get this module's version from the psm */
		psm_set_module_name(g_handle[0], module_applets[i].di_modname);
		psm_unlock_low();

		/* When there is no version in flash, get_version returns 0, so
		   no worry */
		modver = psm_get_version(g_handle[0]);
		if (module_applets[i].di_version > modver)
		{
			/* Let the module handle it's flash variable upgrades */
			__duster_call_module_upgrade_handler(&module_applets[i], modver);
			psm_set_version(g_handle[0], module_applets[i].di_version);
		}
	}

	OSATaskSleep(200*7);
	//psm_show("default psm", g_handle[0]);

	/* Call module initialisations */
	for (i = 0; i < applets_no; i++)
	{
		//CPUartLogPrintf("before module post set, call module initialisations");
		//g_handle = NULL;
		__duster_call_module_post_set_handler(&module_applets[i], DUSTER_CONFIG_START, &dca);
	}

	#if 0
	/*get login limit start@140924*/
	str = psm_get_wrapper("management",		NULL,	"limit_one_user_login");
	if(str && strncmp(str,	"",	1))
	{
		set_single_user_login_setting(atoi(str));
		duster_free(str);
		str = NULL;
	}

	/*get login limit end@140924*/
	#endif
#ifdef QUECTEL_PROJECT_CUST
	/**
	 * add by breeze 2025/01/18
	 * device_name = default device_name + imei last 6 digit, set only once
	 */
	str = psm_get_wrapper("sysinfo", NULL, "device_name_changed");
	if(str && !atoi(str))
	{
		duster_free(str);

		str = psm_get_wrapper("sysinfo", NULL, "device_name");
		imei_str = get_imei(1);
		if(str && imei_str)
		{
			sprintf(new_str, "%s-%s", str, imei_str + strlen(imei_str) - 6);
			psm_set_wrapper("sysinfo", NULL, "device_name", new_str);
			psm_set_wrapper("sysinfo", NULL, "device_name_changed", "1");
		} 
	}
	if(str) duster_free(str);
	str = NULL;
#endif
    /* no need comit here, to reduce flash write */
	//psm_commit__();

	gUI2DialerPara.dusterReadyFlag = 2;

	Duster_module_Printf(1,"%s: finish init duster", __func__);

toEnd:
    if(!TimerDeleteDusterInitTask)
    {
        OSATimerCreate(&TimerDeleteDusterInitTask);
    }
    OSATimerStart(TimerDeleteDusterInitTask, 400, 0, DeleteDusterInitTaskByTimer, 0);
	return;
}

void Save_PSM_Task(void *argc)
{
	OSA_STATUS osa_status;
	UINT8 *msg_buf;
	SavePSMMessage *buf_ptr;
	int buf_len;
	char exitflag=0;

	buf_len = sizeof(SavePSMMessage) + 8;
	if((msg_buf = (UINT8 *)duster_malloc(buf_len)) == NULL)
		return;

	osa_status = OSAMsgQCreate( &gSavePSMMSGQ, "SavePSMMSGQ", sizeof(SavePSMMessage) + 8,
							  16, OSA_PRIORITY);
	Duster_module_Printf(1, "%s: gSavePSMMSGQ=%x",gSavePSMMSGQ);
	ASSERT( osa_status == OS_SUCCESS )

	while(1)
	{
		//receive mssage
		osa_status = OSAMsgQRecv(gSavePSMMSGQ, msg_buf, sizeof(SavePSMMessage), OSA_SUSPEND);
		if(osa_status != OS_SUCCESS )
		{
			ASSERT(0);
		}

		buf_ptr = (SavePSMMessage *)msg_buf;

		switch(buf_ptr->MsgId)
		{
			// save rx/tx statistics data into psm.
		case start_save_psm:
			Duster_module_Printf(1,"%s: rcv start_save_psm",__FUNCTION__);
			SavePsmData();
			SetCfunValue(0);
			break;

		default:
			Duster_module_Printf(1,"wrong messageId:%d",buf_ptr->MsgId);
			break;
		}
	}

    /* unreachable */
#if 0
	if(msg_buf != NULL)
	{
		duster_free(msg_buf);
		msg_buf = NULL;
	}
	duster_Printf("exit  %s",__FUNCTION__);
#endif
}

void CommonATcmdExchangeTask(void *argc)
{
	UINT32 buf_len,i=0;
	OSA_STATUS osa_status;
	UINT8 *msg_buf = NULL,*out_str = NULL,*ptr_CR = NULL;
	unsigned int atLen;
	CommandATMsg *buf_ptr = NULL;
	int ret;
	UINT8 OKFlag;

	char at_str[DIALER_MSG_SIZE] = {'\0'};

	if(gSendATMsgQ==NULL)
	{
		CPUartLogPrintf("gSendATMsgQqueue is NULL!");
		ASSERT(0);
	}
	if(gCommonATMsgQ==NULL)
	{
		CPUartLogPrintf("gCommonATMsgQ is NULL!");
		ASSERT(0);
	}

	buf_len = sizeof(CommandATMsg);
	if((msg_buf = (UINT8 *)duster_malloc(buf_len)) == NULL)
	{
		goto end;
	}

	if((out_str=(UINT8 *)duster_malloc(DIALER_MSG_SIZE))==NULL)
	{
		goto end;
	}
	if((buf_ptr = (CommandATMsg *)duster_malloc(buf_len)) == NULL)
	{
		goto end;
	}

	while(1)
	{

		Duster_module_Printf(3,"%s: wait msg", __FUNCTION__);
		memset(msg_buf,0,buf_len);

		osa_status = OSAMsgQRecv(gSendATMsgQ, msg_buf, buf_len, OSA_SUSPEND);
		if(osa_status != OS_SUCCESS )
			ASSERT(0);

		Duster_module_Printf(1,"%s Recv msg len %d",__FUNCTION__, buf_len);

		memset(buf_ptr,	0,		buf_len);
		memcpy(buf_ptr,	msg_buf,	buf_len);

		if(buf_ptr->atcmd==NULL || strlen(buf_ptr->atcmd) == 0)
			continue;

		if(!out_str) //out_str is freed by other task
		{
			if((out_str=(UINT8 *)duster_malloc(DIALER_MSG_SIZE))==NULL)
			{
        		ASSERT(0);
			}
		}

		memset(out_str,0,DIALER_MSG_SIZE);

		Duster_module_Printf(1,"%s,AT CMD [%s], err_fmt [%s], ok_fmt [%s], ATCMDTYPE [%d]",__FUNCTION__,
								buf_ptr->atcmd,buf_ptr->err_fmt? buf_ptr->err_fmt: "NULL" ,buf_ptr->ok_fmt?buf_ptr->ok_fmt : "NULL", buf_ptr->ATCMD_TYPE);

		if((strncmp(buf_ptr->atcmd,"AT+CMGS",7)==0)||(strncmp(buf_ptr->atcmd,"AT+CMGW",7)==0)) //Attention! AT+CMGS=128/rTPDU
		{
			Duster_module_Printf(3,"%s,meet AT+CMGS or CMGW",__FUNCTION__);
			ptr_CR = (UINT8 *)strchr(buf_ptr->atcmd, '\r');

			if(ptr_CR)
			{
				Duster_module_Printf(1,"%s find \\r",__FUNCTION__);
				memset(at_str,0,DIALER_MSG_SIZE);
				memcpy(at_str, buf_ptr->atcmd, (char *)ptr_CR-buf_ptr->atcmd+1);
				ret=SendATCMDWaitResp(AT_WEB_MSG_CHANNEL, at_str, 150, ">",0,"+CMS ERROR", (char *)out_str, DIALER_MSG_SIZE);
				Duster_module_Printf(1,"%s,recv ret=%d",__FUNCTION__,ret);
				if(ret==0)
				{
					memset(at_str,0,DIALER_MSG_SIZE);
					memset(out_str,0,DIALER_MSG_SIZE);
					Duster_module_Printf(3,"sencond Send AT len=%d",strlen(buf_ptr->atcmd)-((char *)ptr_CR-buf_ptr->atcmd+1));
					memcpy(at_str,ptr_CR+1,strlen(buf_ptr->atcmd)-((char *)ptr_CR-buf_ptr->atcmd+1));
					Duster_module_Printf(3,"at_str is %s",at_str);
					Duster_module_Printf(3,"ok_fmt is %s,ok_flag is %d,err_fmt is %s",buf_ptr->ok_fmt,buf_ptr->ok_flag,buf_ptr->err_fmt);
					ret=SendATCMDWaitResp(AT_WEB_MSG_CHANNEL, at_str, 150, buf_ptr->ok_fmt,buf_ptr->ok_flag,buf_ptr->err_fmt, (char *)out_str, DIALER_MSG_SIZE);
					Duster_module_Printf(3,"%s sencond ret =%d",__FUNCTION__,ret);
				}
				ptr_CR=NULL;
			}
			else
			{
				Duster_module_Printf(1,"%s, can't find \\r,continue",__FUNCTION__);
				continue;
			}
		}
		else
			ret=SendATCMDWaitResp(AT_WEB_MSG_CHANNEL,buf_ptr->atcmd, 150,buf_ptr->ok_fmt,buf_ptr->ok_flag,buf_ptr->err_fmt, (char *)out_str, DIALER_MSG_SIZE);

		if(ret==0)
			OKFlag=1;
		else
			OKFlag=0;
		Duster_module_Printf(1,"%s before callback_func,out_str is %s",__FUNCTION__,out_str);
		if(buf_ptr->callback_func)
			buf_ptr->callback_func(OKFlag,(char *)out_str,buf_ptr->ATCMD_TYPE);

	}

end:
	if(msg_buf)
		duster_free(msg_buf);
	if(out_str)
		duster_free(out_str);
	if (buf_ptr)
		duster_free(buf_ptr);

}

void duster_semaphore_init(void)
{
	OSA_STATUS osaStatus;

	osaStatus = OSASemaphoreCreate(&SentATSema, 1, OSA_FIFO);
	ASSERT(osaStatus == OS_SUCCESS);

	osaStatus = OSASemaphoreCreate(&DialerReadySema, 0, OSA_FIFO);
	ASSERT(osaStatus == OS_SUCCESS);

	osaStatus = OSASemaphoreCreate(&WebSMSSema, 1, OSA_FIFO);
	ASSERT(osaStatus == OS_SUCCESS);

	osaStatus = OSASemaphoreCreate(&WanCellularSema, 1, OSA_FIFO);
	ASSERT(osaStatus == OS_SUCCESS);

#ifdef WEBUI_PB_SUPPORT
	osaStatus = OSASemaphoreCreate(&PhoneBookSema, 1, OSA_FIFO);
	ASSERT(osaStatus == OS_SUCCESS);
#endif
	osaStatus = OSASemaphoreCreate(&SimSmsInitSema, 1, OSA_FIFO);
	ASSERT(osaStatus == OS_SUCCESS);

}

extern void set_default_dhcp(void);

typedef struct{
	UINT16	size ;
	UINT8	*GIDData;
	}SIMGIDFile;

extern 	UINT8 CHAR_TO_UINT8(UINT8 *pInBuf, UINT16 inLen, UINT8 *pOutBuf);
#define REPONSE_DATA_FILE_SIZE_1_OFFSET 2
#define REPONSE_DATA_FILE_SIZE_2_OFFSET 3
#define REPONSE_DATA_FILE_TYPE_OFFSET 6
#define REPONSE_DATA_STRUCTURE_OFFSET 13
#define REPONSE_DATA_RECORD_LENGTH_OFFSET 14
#define GET_REPONSE_EF_SIZE_BYTES 15
#define EF_TYPE_TRABSPARENT 0
#define SIM_FILE_TYPE_EF 4

int Sim_Get_GID(int at_channel,	SIMGIDFile* GID1file)
{
	char at_str[64] = {'\0'};
	char resp_str[64] = {'\0'};
	int ret = 0;
	char tmp[64] = {'\0'};
	char temp[3] = {'\0'};
	int i = 0;
	char *p;
	char acsii_char;
	char *endptr = NULL;
	char tempUINT8[64] = {'\0'};
	UINT16 size = 0;

	Duster_module_Printf(1,"%s Enter", __FUNCTION__);
	if(!GID1file)
	{
		return -1;
	}

	sprintf(at_str, "AT+CRSM=192,28478,0,0,15,,3F007FFF\r");
	ret = SendATCMDWaitResp(at_channel, at_str, 150, "+CRSM",1,"+CME ERROR", resp_str, sizeof(resp_str));
	if(ret != 0)
	{
		Duster_module_Printf(1,"send AT+CRSM to get reponse failed %d",ret);
		return -1;
	}
	else
	{

		Duster_module_Printf(1,"get GID1 reponse ret %s",resp_str);
		{
			if(sscanf(resp_str,"%*[^:]:%*[^,],%*[^,],%s",tmp) != 1)
			{
				Duster_module_Printf(1,"parse GID1 reponse failed");
				Duster_module_Printf(1,"%s Leave", __FUNCTION__);
				return -1;
			}
			CHAR_TO_UINT8((UINT8 *)tmp,	strlen(tmp), (UINT8 *)tempUINT8);

			if(SIM_FILE_TYPE_EF != tempUINT8[REPONSE_DATA_FILE_TYPE_OFFSET])
			{
				return -1;
			}

			if(EF_TYPE_TRABSPARENT != tempUINT8[REPONSE_DATA_STRUCTURE_OFFSET])
			{
				return -1;
			}
			size = ((tempUINT8[REPONSE_DATA_FILE_SIZE_1_OFFSET] & 0xFF) << 8) + (tempUINT8[REPONSE_DATA_FILE_SIZE_2_OFFSET] & 0xFF);
		}
	}
	GID1file->size = size;

	if(size > 0)
	{
		memset(at_str,	'\0',	64);
		sprintf(at_str, "AT+CRSM=176,28478,0,0,%d,,3F007FFF\r",	size);
		ret = SendATCMDWaitResp(at_channel, at_str, 150, "+CRSM",1,"+CME ERROR", resp_str, sizeof(resp_str));
		if(ret != 0)
		{
			Duster_module_Printf(1,"send AT+CRSM to get GID1 failed %d",ret);
			return -1;
		}
		else
		{

			Duster_module_Printf(1,"get GID1  ret %s",resp_str);
			{
				if(sscanf(resp_str,"%*[^:]:%*[^,],%*[^,],%s",tmp) != 1)
				{
					Duster_module_Printf(1,"parse GID1 FILE failed");
					Duster_module_Printf(1,"%s Leave", __FUNCTION__);
					return -1;
				}
				CHAR_TO_UINT8((UINT8 *)tmp,	strlen(tmp), (UINT8 *)tempUINT8);
				GID1file->GIDData = malloc(size);
				if(!GID1file->GIDData)
				{
					return -1;
				}
				memcpy(GID1file->GIDData,	tempUINT8,	size);
			}
		}
	}
	Duster_module_Printf(1,	"%s GID1file->size is %d",	__FUNCTION__,	GID1file->size);
	if(GID1file->GIDData)
	{
		for(i = 0; i< GID1file->size;	i++)
		{
			CPUartLogPrintf("%s GID1file->GID_data[%d] is %02x",	__FUNCTION__,	i,	GID1file->GIDData[i]);
		}
	}
	Duster_module_Printf(1,"%s Leave", __FUNCTION__);
	return 0;
}

typedef struct
{
	int dis_cond;
	int spn_exist;
	char spn_name[17];
} SIMSpnInfo;

/*===========================================================================

	Get SPN name form SIM CARD.
	if(spn_exist)
	{
		//get SPN name
		memcpy(buffer , spn_name,	strlen(spn_name))
	}

============================================================================*/
int Sim_Get_Spn(SIMSpnInfo *spn_info, int at_channel)
{
	char at_str[64] = {'\0'};
	char resp_str[64] = {'\0'};
	int ret = 0;
	char tmp[64] = {'\0'};
	char temp[3] = {'\0'};
	int i = 0;
	char *p;
	char acsii_char;
	char *endptr = NULL;

	Duster_module_Printf(1,"%s Enter", __FUNCTION__);

	if(is_usim_card())
       	sprintf(at_str, "AT+CRSM=176,28486,0,0,17,,3F007FFF\r");
	else
		sprintf(at_str, "AT+CRSM=176,28486,0,0,17,,3F007F20\r");

	ret = SendATCMDWaitResp(at_channel, at_str, 150, "+CRSM",1,"+CME ERROR", resp_str, sizeof(resp_str));
	if(ret != 0)
	{
		Duster_module_Printf(1,"send AT+CRSM to get SPN failed %d",ret);
		return -1;
	}
	else
	{
		Duster_module_Printf(1,"get SPN ret %s",resp_str);
		{
			if(sscanf(resp_str,"%*[^:]:%*[^,],%*[^,],%s",tmp) != 1)
			{
				Duster_module_Printf(1,"parse SPN failed");
				Duster_module_Printf(1,"%s Leave", __FUNCTION__);
				return -1;
			}
			memcpy(temp,tmp,2);
			temp[2] = '\0';
			Duster_module_Printf(1,"SPN dis_cond %d",atoi(temp));
			p = tmp+2;
			spn_info->dis_cond = atoi(temp);
			while(i<16)
			{
				memcpy(temp,p,2);
				temp[2] = '\0';
				if(strcmp(temp,"FF") == 0)
					break;
				spn_info->spn_exist = 1;
				acsii_char = (char)strtoul(temp,&endptr,16);
				spn_info->spn_name[i] = acsii_char;
				Duster_module_Printf(1,"%d %c",acsii_char,acsii_char);
				i++;
				p = p+2;
			}
		}
	}
	Duster_module_Printf(1,"%s Leave", __FUNCTION__);
	return 0;
}
/*=====================================================
	Array sbuset should be larger than 5
	As Customer asked for 4 digits after MCCMNC
======================================================*/
extern 	char IMSI[16];
extern 	Mnc_Apn *IMSI_APN ;
int get_sim_Subset(char *subset)
{
	Mnc_Apn *getAPNInfo = NULL;
	UINT8	mncLen = 0;

	if((strlen(IMSI) == 0) || !subset)
	{
		return -1;
	}
	/*need to determine the length of MNC*/
	if(!IMSI_APN)
	{
		IMSI_APN = wan_get_apn((CMSimID)0);
		getAPNInfo = IMSI_APN;
	}
	else
	{
		getAPNInfo = IMSI_APN;
	}

	if(getAPNInfo && getAPNInfo->num)
	{
		//mncLen = strlen(getAPNInfo->mcc);
		memcpy(subset,	IMSI + 3 + gMncLen,	4);
	}
	Duster_module_Printf(1,	"%s Network subset is %s ",	__FUNCTION__,	subset);
	return 1;
}

/*API TO TURN DATALINK OFF*/
void wan_cellular_data_off()
{
	wan_cellular_stop_cm();
	psm_set_wrapper(PSM_MOD_WAN, "cellular", "manual_network_start",	"1");
}
extern int wanAction ;
/*API TO TURN DATALINK ON*/
void wan_cellular_data_on(void)
{
	WlanClientStatusMessage  status_msg;
	OS_STATUS osaStatus;
	wanAction = DUSTER_CONFIG_SET;
	psm_set_wrapper(PSM_MOD_WAN, "cellular", "manual_network_start",	"0");
	status_msg.MsgId = CellularStart;
	status_msg.MsgData = NULL;
	osaStatus = OSAMsgQSend(gWlanIndMSGQ, sizeof(status_msg), (UINT8 *)&status_msg, OSA_NO_SUSPEND);
}

int duster_init_wan(void)
{
	Connection_Setup_Context *default_setup_pdp = NULL;
	Duster_module_Printf(2,"%s: ENTER",__FUNCTION__);

	while (!ATCmdSvrRdy)
	{
		OSATaskSleep(10);
	}

	if (duster_auto_select_netwrok((int)DUSTER_INIT_ATP_INDEX) != 0)
	{
		Duster_module_Printf(1,"%s: set auto select network failed",__FUNCTION__);
	}

	if(wan_sim_check(DUSTER_CONFIG_START, DUSTER_INIT_ATP_INDEX) != 0)
	{
	      Duster_module_Printf(1,"%s: check_SIM_status failed",__FUNCTION__);
		return -1;
	}
	if (getConnectMode() == CONNECT_MODE_AUTO && getAutoApn() == APN_MODE_AUTO)
	{
		default_setup_pdp = (Connection_Setup_Context *)duster_malloc(sizeof(Connection_Setup_Context));
		if(!default_setup_pdp)
		{
			Duster_module_Printf(1,"%s: duster malloc size %d  failed", __FUNCTION__, sizeof(Connection_Setup_Context));
			return -1;
		}
		memset(default_setup_pdp, 0, sizeof(Connection_Setup_Context));
		default_setup_pdp->connection_num = 0x00;
		strcpy(default_setup_pdp->PDP_name, DEFAULT_PDN_NAME);
		if(!default_setup_pdp->pdpinfo)
		{
			default_setup_pdp->pdpinfo = (PdpInfo *)duster_malloc(sizeof(PdpInfo));
			if(!default_setup_pdp->pdpinfo)
			{
				Duster_module_Printf(1,"%s: duster malloc size %d  failed", __FUNCTION__, sizeof(PdpInfo));
				goto end;
			}
			memset(default_setup_pdp->pdpinfo, 0, sizeof(PdpInfo));
		}
		strcpy(default_setup_pdp->pdpinfo->APN, DEFAULT_APN);
		default_setup_pdp->pdpinfo->IP_Type = DEFAULT_IPTYPE;
		default_setup_pdp->pdpinfo->PDP_Type = DEFALT_PDPTYPE;
		default_setup_pdp->pdpinfo->IsDefaultConnection = 1;

		CM_Create_Connection(default_setup_pdp);
	}

end:
	if (default_setup_pdp)
	{
		if (default_setup_pdp->pdpinfo)
			duster_free(default_setup_pdp->pdpinfo);

		duster_free(default_setup_pdp);
	}
	Duster_module_Printf(2,"%s: LEAVE",__FUNCTION__);
	return 0;
}

/****************************************************
*	input : MAC :Mac address of kicked off clent
*			client_type : 0 -- WEBUI;	1 --APP
*
*****************************************************/
void kickoff_client_notify(char *mac,	UINT8 client_type)
{

}

/*get <message><name_spec_match/></message>*/
extern int get_spec_name_match(void);
char *	SMS_Find_Name_From_PB(char * input_num,	s_MrvFormvars *pb_local_list,	s_MrvFormvars* pb_sim_list)
{
	char 			*return_name = NULL;
	PB_Entry 		*tmp_entry = NULL;
	s_MrvFormvars 		*tmp_node = NULL;
	PB_ExtraInfo	*tmp_extrainfo = NULL;
	UINT8		 start_pos = 0;			/*for person sms,prefix num "+86"*/
	int 	name_match_rule;

	Duster_module_Printf(1,	"enter %s",	__FUNCTION__);
	if(!input_num)
	{
		goto EXIT;
	}
	Duster_module_Printf(1,	"%s input_num is %s",	__FUNCTION__,	input_num);

	if(*input_num == '+')
	{
		start_pos = 2;		/*cc code can be 1 to 3 otcs*/
	}

	name_match_rule = get_spec_name_match();
	/*search Local PB list first*/

	tmp_node = pb_local_list;
	while(tmp_node)
	{
		tmp_entry = (PB_Entry *)tmp_node->value;
		if(tmp_entry->mobile_Num)
		{
			if(name_match_rule == 0)
			{
				if(!strcmp(input_num,	tmp_entry->mobile_Num)
				        || !strcmp(input_num + start_pos,	tmp_entry->mobile_Num)		/*1 octs 	cc code*/
				        || !strcmp(input_num + start_pos + 1,	tmp_entry->mobile_Num)	/*2 octs  cc code */
				        || !strcmp(input_num + start_pos + 2,	tmp_entry->mobile_Num)) /*3 octs  cc code */
				{
					return_name = tmp_entry->name;
					goto EXIT;
				}
				/*for china SP number , begin with 1065X 1065795555	-- cmbc*/
				if(!strncmp(input_num,	"1065",	4))
				{
					if(!strcmp(input_num + 5,	tmp_entry->mobile_Num))
					{
						return_name = tmp_entry->name;
						goto EXIT;
					}
				}
				if(tmp_entry->mobile_Num[0] == '0')
				{
					if(!strcmp(input_num + start_pos,	tmp_entry->mobile_Num + 1)		/*1 octs 	cc code*/
				        || !strcmp(input_num + start_pos + 1,	tmp_entry->mobile_Num + 1)	/*2 octs  cc code */
				        || !strcmp(input_num + start_pos + 2,	tmp_entry->mobile_Num + 1))
					{
						return_name = tmp_entry->name;
						goto EXIT;
					}
				}
			}
			else if(name_match_rule == 1)
			{
				/*for Notion's special requrie; compare last 10 digits*/
				if(strlen(input_num) >= 10 && strlen(tmp_entry->mobile_Num) >= 10)
				{
					if(!strcmp(input_num + strlen(input_num) - 10,
						tmp_entry->mobile_Num + strlen(tmp_entry->mobile_Num) -10))
						{
							return_name = tmp_entry->name;
							goto EXIT;
						}
				}
				if((strlen(input_num) < 10) && (strlen(tmp_entry->mobile_Num) < 10))
				{
					if(!strcmp(input_num,	tmp_entry->mobile_Num))
					{
						return_name = tmp_entry->name;
						goto EXIT;
					}

				}
			}
		}

		if(tmp_entry->extraInfo)
		{
			tmp_extrainfo = (PB_ExtraInfo *)tmp_entry->extraInfo;
			if(tmp_extrainfo->home_Num)
			{
				if(name_match_rule == 0)
				{
					if(!strcmp(input_num,	tmp_extrainfo->home_Num)
					        || !strcmp(input_num + start_pos,	tmp_extrainfo->home_Num)		/*1 octs 	cc code*/
					        || !strcmp(input_num + start_pos + 1,	tmp_extrainfo->home_Num)	/*2 octs  cc code */
					        || !strcmp(input_num + start_pos + 2,	tmp_extrainfo->home_Num)) /*3 octs  cc code */
					{
						return_name = tmp_entry->name;
						goto EXIT;
					}
					/*for china SP number , begin with 1065X 1065795555	-- cmbc*/
					if(!strncmp(input_num,	"1065",	4))
					{
						if(!strcmp(input_num + 5,	tmp_extrainfo->home_Num))
						{
							return_name = tmp_entry->name;
							goto EXIT;
						}
					}
					if(tmp_extrainfo->home_Num[0] == '0')
					{
						if(!strcmp(input_num + start_pos,	tmp_extrainfo->home_Num + 1)		/*1 octs	cc code*/
							|| !strcmp(input_num + start_pos + 1,	tmp_extrainfo->home_Num + 1)	/*2 octs  cc code */
							|| !strcmp(input_num + start_pos + 2,	tmp_extrainfo->home_Num + 1))
						{
							return_name = tmp_entry->name;
							goto EXIT;
						}
					}
				}
				else if(name_match_rule == 1)
				{
					/*for Notion's special requrie; compare last 10 digits*/
					if(strlen(input_num) >= 10 && strlen(tmp_extrainfo->home_Num) >= 10)
					{
						if(!strcmp(input_num + strlen(input_num) - 10,
							tmp_extrainfo->home_Num + strlen(tmp_extrainfo->home_Num) -10))
							{
								return_name = tmp_entry->name;
								goto EXIT;
							}
					}
					if((strlen(input_num) < 10) && (strlen(tmp_extrainfo->home_Num) < 10))
					{
						if(!strcmp(input_num,	tmp_extrainfo->home_Num))
						{
							return_name = tmp_entry->name;
							goto EXIT;
						}

					}
				}
			}
			if(tmp_extrainfo->office_Num)
			{
				if(name_match_rule == 0)
				{
					if(!strcmp(input_num,	tmp_extrainfo->office_Num)
					        || !strcmp(input_num + start_pos,	tmp_extrainfo->office_Num)		/*1 octs 	cc code*/
					        || !strcmp(input_num + start_pos + 1,	tmp_extrainfo->office_Num)	/*2 octs  cc code */
					        || !strcmp(input_num + start_pos + 2,	tmp_extrainfo->office_Num)) /*3 octs  cc code */
					{
						return_name = tmp_entry->name;
						goto EXIT;
					}
					/*for china SP number , begin with 1065X 1065795555	-- cmbc*/
					if(!strncmp(input_num,	"1065",	4))
					{
						if(!strcmp(input_num + 5,	tmp_extrainfo->office_Num))
						{
							return_name = tmp_entry->name;
							goto EXIT;
						}
					}
					if(tmp_extrainfo->office_Num[0] == '0')
					{
						if(!strcmp(input_num + start_pos,	tmp_extrainfo->office_Num + 1)		/*1 octs	cc code*/
							|| !strcmp(input_num + start_pos + 1,	tmp_extrainfo->office_Num + 1)	/*2 octs  cc code */
							|| !strcmp(input_num + start_pos + 2,	tmp_extrainfo->office_Num + 1))
						{
							return_name = tmp_entry->name;
							goto EXIT;
						}
					}
				}
				else if(name_match_rule == 1)
				{
					/*for Notion's special requrie; compare last 10 digits*/
					if(strlen(input_num) >= 10 && strlen(tmp_extrainfo->office_Num) >= 10)
					{
						if(!strcmp(input_num + strlen(input_num) - 10,
							tmp_extrainfo->office_Num + strlen(tmp_extrainfo->office_Num) -10))
							{
								return_name = tmp_entry->name;
								goto EXIT;
							}
					}
					if((strlen(input_num) < 10) && (strlen(tmp_extrainfo->office_Num) < 10))
					{
						if(!strcmp(input_num,	tmp_extrainfo->office_Num))
						{
							return_name = tmp_entry->name;
							goto EXIT;
						}

					}
				}


			}
		}

		tmp_node = tmp_node->next;
	}

	/*if not find match name in local pb list,  begin search sim pb list*/
	tmp_node = pb_sim_list;
	while(tmp_node)
	{
		tmp_entry = (PB_Entry *)tmp_node->value;
		if(tmp_entry->mobile_Num)
		{
			if(name_match_rule == 0)
			{
			if(!strcmp(input_num,	tmp_entry->mobile_Num)
			        || !strcmp(input_num + start_pos,	tmp_entry->mobile_Num)		/*1 octs 	cc code*/
			        || !strcmp(input_num + start_pos + 1,	tmp_entry->mobile_Num)	/*2 octs  cc code */
			        || !strcmp(input_num + start_pos + 2,	tmp_entry->mobile_Num)) /*3 octs  cc code */
			{
				return_name = tmp_entry->name;
				goto EXIT;
			}
				if(!strncmp(input_num,	"1065",	4))
				{
					if(!strcmp(input_num + 5,	tmp_entry->mobile_Num))
					{
						return_name = tmp_entry->name;
						goto EXIT;
					}
				}
				if(tmp_entry->mobile_Num[0] == '0')
				{
					if(!strcmp(input_num + start_pos,	tmp_entry->mobile_Num + 1)		/*1 octs 	cc code*/
				        || !strcmp(input_num + start_pos + 1,	tmp_entry->mobile_Num + 1)	/*2 octs  cc code */
				        || !strcmp(input_num + start_pos + 2,	tmp_entry->mobile_Num + 1))
					{
						return_name = tmp_entry->name;
						goto EXIT;
					}
				}
			}
			else if(name_match_rule == 1)
			{
			/*for Notion's special requrie; compare last 10 digits*/
			if(strlen(input_num) >= 10 && strlen(tmp_entry->mobile_Num) >= 10)
			{
				if(!strcmp(input_num + strlen(input_num) - 10,
						tmp_entry->mobile_Num + strlen(tmp_entry->mobile_Num) -10))
					{
						return_name = tmp_entry->name;
						goto EXIT;
					}
			}
				if((strlen(input_num) < 10) && (strlen(tmp_entry->mobile_Num) < 10))
			{
					if(!strcmp(input_num,	tmp_entry->mobile_Num))
				{
					return_name = tmp_entry->name;
					goto EXIT;
					}
				}
			}
		}

		tmp_node = tmp_node->next;
	}


EXIT:
	Duster_module_Printf(1,	"leave %s",	__FUNCTION__);
	return return_name;
}

void switch_image_after_restore(void)
{
	/*
	* important, if customer want to change version to LTG or LWG
	* should comment out #if 0
	*/
	#if 0
	SwitchImageOnly(PLATFORM_5MODE_LWG_VER);
	#endif
}

int key_ussd_set(int action, int at_channel, char *ussd_code)
{
	int ret;
	psm_set_wrapper("ussd", NULL, "ussd_param", ussd_code);
	ret =  process_ussd_action((enum e_ui_ussd_action)action,at_channel);
	psm_set_wrapper("ussd", NULL, "ussd_param", "");
	return ret;
}

int trigger_ui_ussd(char *ucs2_result)
{
	//send display event to OLED


	return 0;
}



