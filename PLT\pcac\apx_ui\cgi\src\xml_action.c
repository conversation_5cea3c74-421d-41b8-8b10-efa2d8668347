/* -*- linux-c -*- */
//#include <syslog.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "cgi.h"
#include "UART.h"
//#include <unistd.h>
#include "mongoose.h"
#include "osa.h"
#include "FDI_TYPE.h"
#include "FDI_FILE.h"
//#include "memwatch.h"
#include "rdiskfsys.h"
#include "fbf_parse.h"
#include "psm_wrapper.h"
#include "FlashPartition.h"
#include "flash_api.h"
#include "lfs_cache.h"
#include "mbedtls/sha1.h"
#include "mbedtls/compat-1.3.h"
#include "wan_applet.h"
#include "bsp.h"
#include "duster.h"
#include "lwip_api.h"
#ifdef TR069_SUPPORT
#include "tr069.h"
#endif
#include "platform.h"
#include "watchdog.h"
#ifdef FOTA_SUPPORT
#include "download.h"
#endif
#include "csw_mem.h"
#include "utilities.h"
#include "dialer_task.h"
#include "common_print.h"
#include "utilities.h"
#ifdef LFS_SUPPORT
#include "lfs_cache.h"
#endif
//#include "cgi_common.h"
#ifdef CRANE_WEBUI_SUPPORT
//#include "tc_defs.h"
//#include "nucleus.h"
#endif


#define ARG_METHOD        "method"
#define ARG_FILE          "file"
#define ARG_MODULE        "module"

#define XML_HTML_HEAD_SIZE 256
#define XML_GET_BUF_SIZE 10*4096
#define CGI_STACK_SIZE 4*4096
#define CGI_TASK_PRIORITY 151
#define SERVER_TO_CGI_MESSAGE_Q_SIZE 16
#define CGI_TO_SERVER_MESSAGE_Q_SIZE 16
#define PATH_MAX 256
#define snprintf _snprintf
#define MAX_UPGRADE_SIZE  (30 << 20)// Max size 30MB, rjin 20140324
//#define assert(x) DIAG_ASSERT(x)

#define assert(x) DIAG_ASSERT(x)
#define sleep(x) OSATaskSleep((x) * 1000)//second
#define UPGRADE_FLAG_ADDR_BASE DDR_UPGRADE_RST_FLAG_ADDR
#define flash_sector_size 8192
#define MSG_DOWNLOAD_UPGRADE_FILE_ABORT 0xaa
#define MSG_DOWNLOAD_UPGRADE_FILE_END 0x99

__align(4) unsigned char CgiTaskStack[CGI_STACK_SIZE];
void *CgiTaskRef = NULL;
s_MrvFormvars *gstart = NULL;
OSMsgQRef ServerToCgiEnvMsgQ;
int restore_config=0;
unsigned int sw_update_flag = 0;
BOOL InUpgradeReset = FALSE;

static OSMsgQRef  CgiToServerMsgQ_APP;
static OSMsgQRef  ServerToCgiMsgQ_APP;

extern s_firmware_status g_upgrade_fw_status;
//Semaphore mongoose.c
extern OSSemaRef _cgiparamRef;
extern OSSemaRef _cgiparamreadyRef;
//MsgQ mongoose.c
extern OSMsgQRef ServerToCgiMsgQ;
extern OSMsgQRef CgiToServerMsgQ;
extern OSMsgQRef CgiToServerMsgQ1;

extern struct cgi_env_block g_cgi_param;
extern struct cgi_env_block *pg_cgi_param;
extern char g_current_mac[18];
extern char g_current_upgrade_mac[18];

extern unsigned int lock_ip;
extern unsigned int g_session_time;
#ifdef TR069_SUPPORT
extern struct cpe_struct cpe;
#endif

UINT32 get_flash_block_size(void);
int set_obm_upgrade_flag(int file_size);
void notify_server_download_firmware_done(void);
void set_system_in_upgrade_status(void);
void clear_system_in_upgrade_status(void);


extern char *MrvGetEnv(struct cgi_env_block *pcgiparam,
                              const char *name);
extern void cgi_process_form_test1(void);
extern int duster_parser_nucleus(char *method, char *filename, char *fileres);
extern void watchdog_reset(void);
extern void SetFactoryRstFlag(BOOL flag);
//LED show for samsung platform
extern void LED_SWUpgrade_On(void);
extern void LED_SWUpgrade_Off(void);
extern void Flash_EraseFATPartition(void);
extern void GKIExtTickFuncCallBack(UINT32 deltaInTicks);
extern void L1GKIExtTickFuncCallBack(UINT32 deltaInTicks);
extern void TickUnSubmit( TickCallbackPtr callback_ptr);
extern void wait_cfun_done(void);
extern BOOL CheckIf32MNOR( void );
extern int GetCfunValue(void);
extern void SetCfunValue(int value);

#if 0
extern void fbf_hash_start(sha1_context *pctx);
extern void fbf_hash_update( sha1_context *pctx, const unsigned char *input, size_t ilen );
extern void fbf_hash_out( sha1_context *pctx, unsigned char output[20] );
extern void fbf_hash_end( sha1_context *pctx);
#endif

extern UINT32 image_valid_check(ImginfoTable *imginfo_table, UINT32 buffer);
extern int is_fbf_version_valid(char *fbf_version);
extern int firmware_verify(char *sig_buf, int sig_len, s_fbf_buf_ctx *ctx);
extern int check_battery_electic_enough(void);
extern void pre_flash_firmware(void);
extern int get_need_cgi_response_flag(void);
extern void pre_handle_upgrade(void);
extern void lwip_set_ul_non_cpy(int flag);
extern void swupgrade_begin_led_on(void);
extern void swupgrade_done_led_on(int status);
extern UINT32 backup_firmware(void);
extern void FatCacheLock(BOOL lock);
extern int strcasecmp(const char *s1, const char *s2);
extern UINT32 get_fbf_version(void * img_addr, char *fbf_version);
extern UINT32 get_platform_mode_version(void);
extern UINT32 burn_fbf_file_2flash(UINT32 image_index, UINT32 image_size, UINT32 buffer);
extern UINT32 notify_obm_webui_upgrade_flag(ImginfoTable *image_table);
extern void dump_buffer(const char *name, char *buf, int len);

//extern void set_td_interface_status(int status);
extern int SendATCMDWaitResp(int sATPInd,char *in_str, int timeout, char *ok_fmt, int ok_flag, char *err_fmt, char *out_str, int resplen);

UINT32 ddr_msa_ir_buffer_base;
extern UINT32 ddr_msa_ir_buffer_size;
UINT32 ddr_dsp_ro_base;
UINT32 ddr_dsp_ro_size;
UINT32 ddr_ps_noncache_data_base;
UINT32 ddr_ps_noncache_data_size;

#ifdef WEBUI_SUPPORT
#define CGI_module_Printf(level, fmt,args...) \
	{if((module_level[Module_CGI] != 0) && (level <= module_level[Module_CGI])) CPUartLogPrintf(fmt, ##args);}
#else
#define CGI_module_Printf(level, fmt,args...)
#endif

#define MBEDTLS_RSA_PKCS_V15    0
#define MBEDTLS_RSA_PUBLIC      0
#define UPGRADE_ERR_SYSTEM 		10


#define cgi_log(fmt,args...)	{CGI_module_Printf(3,fmt, ##args);}
#define cgi_critic(fmt,args...)	{CGI_module_Printf(2,fmt, ##args);}
#define cgi_err(fmt,args...)	{CGI_module_Printf(1,fmt, ##args);}

#define rsa_context mbedtls_rsa_context
#define rsa_init mbedtls_rsa_init
#define RSA_PKCS_V15 MBEDTLS_RSA_PKCS_V15
#define mpi_read_string mbedtls_mpi_read_string
#define RSA_E   "10001"
#define rsa_pkcs1_verify mbedtls_rsa_pkcs1_verify
#define RSA_PUBLIC MBEDTLS_RSA_PUBLIC
#define POLARSSL_MD_SHA1 MBEDTLS_MD_SHA1
#define rsa_free mbedtls_rsa_free

//#define FBF_FILE_HEAD 4096
char recv_buf[FBF_FILE_HEAD_SIZE + 1];
//#define MAX_IMAGE_BUFFER 128*1024
enum {
	ERR_NEED_OBM_UPGRAGE_OK = 0x10,
	ERR_NEED_OBM_UPGRAGE_MEM,
	ERR_NEED_OBM_UPGRAGE_FILE_INVALID,
	ERR_NEED_OBM_UPGRAGE_FLASHIO,
	ERR_NEED_OBM_UPGRAGE_HTTP,
	ERR_NEED_OBM_UPGRAGE_FILE_SIZE,
	ERR_NEED_OBM_UPGRAGE_NOT_ALLOW,
};

void SetCFUN0(void)
{
	SetCfunValue(0);
	OSATaskSleep(100);
	wait_cfun_done();
}

void set_cfun0_for_upgrade(void)
{
    int ret = 0;
    char res_str[100] = { 0 };

    clear_system_in_upgrade_status();
  	// bring down interface with PS
	netif_set_default_nw_status(0);
	OSATaskSleep(100);

    ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "ATH\r", 60, NULL, 1, "+CME ERROR", res_str, sizeof(res_str));
    CPUartLogPrintf("%s: set ATH done with ret %d",__FUNCTION__, ret);

    set_system_in_upgrade_status();
	if(GetCfunValue() != 0)
	{
		CPUartLogPrintf("%s: set CFUN0",__FUNCTION__);
		SetCfunValue(0);
		OSATaskSleep(100);
		wait_cfun_done();
	}

	// deactive watchdog before upgrade.
	watchdogDeactivate();
}

int slist_count(s_MrvFormvars *start)
{
	s_MrvFormvars *begin;
	begin = start;
	int count = 0;

	while(begin) {
		count++;
		begin = begin->next;
	}
	return count;
}

void clear_ps_buf(void)
{
#ifdef WEBUI_FULL_UPGRADE_SUPPORT
	if (!support_unify_upgrade())
	{
		if(!PlatformIsMinSystem())
		{
			TickUnSubmit(L1GKIExtTickFuncCallBack);
			TickUnSubmit(GKIExtTickFuncCallBack);
		}
		memset((UINT32 *)ddr_msa_ir_buffer_base, 0x00, ddr_msa_ir_buffer_size);// 7MB DDR buf
		memset((UINT32 *)ddr_dsp_ro_base, 0x00, ddr_dsp_ro_size);// 2.5MB DDR buf
		memset((UINT32 *)ddr_ps_noncache_data_base, 0x00, ddr_ps_noncache_data_size);// 2.5MB DDR buf
	}
#endif
}

void fbf_buf_ctx_init(s_fbf_buf_ctx *pctx)
{
	memset(pctx, 0, sizeof(s_fbf_buf_ctx));

#ifdef WEBUI_FULL_UPGRADE_SUPPORT
	if (!support_unify_upgrade())
	{
		pctx->buffer_start = (UINT8 *)ddr_msa_ir_buffer_base;
		pctx->buffer_len = ddr_msa_ir_buffer_size;
		pctx->buffer2_start = (UINT8 *)ddr_dsp_ro_base;
		pctx->buffer2_len = ddr_dsp_ro_size;
		pctx->buffer3_start = (UINT8 *)ddr_ps_noncache_data_base;
		pctx->buffer3_len = ddr_ps_noncache_data_size;
		CPUartLogPrintf("%s:buffer_start[0x%lx],buffer_len[0x%lx],buffer2_start[0x%lx],buffe2r_len[0x%lx],buffer3_start[0x%lx],buffer3_len[0x%lx]",
						__func__, pctx->buffer_start, pctx->buffer_len, pctx->buffer2_start, pctx->buffer2_len, pctx->buffer3_start, pctx->buffer3_len);
	}
#endif
}

/*
param
sig: ǩ��buffer��ʹ��hex��ʽ
sig_len: ǩ��buffer����
plaintext: ԭ��buffer
pl_len:  ԭ��buffer����

��֤����:
1,read public key from RSA_N & RSA_E;
2,init rsa context;
3,plaintext -> hash(sha1) => hashcode
4,sig,hashcode->rsa_pkcs1_verify=>result
*/

#if 0
int rsa_verify(unsigned char *sig, int sig_len, const char *plaintext, int pl_len)
{
    int ret;
    rsa_context rsa;
    unsigned char hash[20];
    ret = 1;

    printf( "Reading public key" );

    rsa_init( &rsa, RSA_PKCS_V15, 0 );

    if( ( ret = mpi_read_string( &rsa.N, 16, g_RSA_N ) ) != 0 ||
        ( ret = mpi_read_string( &rsa.E, 16, RSA_E ) ) != 0 )
    {
        printf( " failed  ! mpi_read_file returned %d", ret );
        goto exit;
    }

    rsa.len = ( mpi_msb( &rsa.N ) + 7 ) >> 3;

    /*
     * Extract the RSA signature from the text file
     */
    ret = 1;

    if( sig_len != rsa.len )
    {
        printf( "Invalid RSA signature format\n\n" );
        goto exit;
    }

    /*
     * Compute the SHA-1 hash of the input file and compare
     * it with the hash decrypted from the RSA signature.
     */
    printf( "Verifying the RSA/SHA-1 signature %s", plaintext);

    //sha1(plaintext, pl_len, hash);

#if 0
{
    //add output log
    int i = 0;
    printf( "hash:");
    for(i=0; i<20; i++)
        printf( "%02x ", hash[i]);
}
#endif

    if( ( ret = rsa_pkcs1_verify( &rsa, NULL,NULL,RSA_PUBLIC,
                                  POLARSSL_MD_SHA1, 20, (unsigned char *)plaintext, sig ) ) != 0 )
    {
        printf( " failed  ! rsa_pkcs1_verify returned -0x%0x", -ret );
        goto exit;
    }

    printf( "OK (the decrypted SHA-1 hash matches)" );

    ret = 0;

exit:
	rsa_free( &rsa );
    return( ret );
}
#endif

UINT32 store_data_into_buf(s_fbf_buf_ctx *pctx, UINT8 *data, UINT32 padding_len)
{
	UINT32 len;
	UINT32 len2;
	UINT32 offset;
	// check if buf is enough
	if(pctx->bytes_receive + padding_len > pctx->buffer_len + pctx->buffer2_len + pctx->buffer3_len)
	{
		//
		CPUartLogPrintf("%s [Fatal Error], buf limited, total 0x%x, cur 0x%x, new add 0x%x ",__FUNCTION__, pctx->buffer_len +  pctx->buffer2_len, pctx->bytes_receive,padding_len);
		return NotEnoughMomeryError;
	}
	if(pctx->bytes_receive >=  pctx->buffer_len + pctx->buffer2_len)
	{
		offset = pctx->bytes_receive - (pctx->buffer_len + pctx->buffer2_len);
		memcpy(pctx->buffer3_start + offset, data, padding_len);// buf3
	}
	else if((pctx->bytes_receive + padding_len >  (pctx->buffer_len + pctx->buffer2_len))
		 && (pctx->bytes_receive <  (pctx->buffer_len + pctx->buffer2_len)))
	{
		len = (pctx->buffer_len + pctx->buffer2_len) - pctx->bytes_receive;
		len2 = padding_len - len;
		memcpy((pctx->buffer2_start + pctx->bytes_receive - pctx->buffer_len), data, len);// buf2
		memcpy(pctx->buffer3_start, data + len, len2);// buf3
	}
	else if((pctx->bytes_receive + padding_len <=  (pctx->buffer_len + pctx->buffer2_len))
		 && (pctx->bytes_receive >=  pctx->buffer_len))
	{
		memcpy((pctx->buffer2_start + (pctx->bytes_receive - pctx->buffer_len)), data, padding_len);// buf2
	}
	else if(pctx->bytes_receive + padding_len <=  pctx->buffer_len)
	{
		memcpy((pctx->buffer_start + pctx->bytes_receive), data, padding_len);// buf1
	}
	else
	{
		len = pctx->buffer_len - pctx->bytes_receive;
		len2 = padding_len - len;
		memcpy((pctx->buffer_start + pctx->bytes_receive), data, len);// buf1
		memcpy(pctx->buffer2_start, data + len, len2);// buf2
	}

	pctx->bytes_receive += padding_len;

	return NoError;
}

static void set_upgrade_status(UINT32 status)
{
	g_upgrade_fw_status.status = status;
}

static void set_upgrade_fail_cause(UINT32 cause)
{
	g_upgrade_fw_status.cause = cause;
}

static void set_upgrade_progress(UINT32 writen, UINT32 total)
{
	if(writen == total)
		g_upgrade_fw_status.progress = 100;
	else if(writen < total)
		g_upgrade_fw_status.progress = (int) ((writen * 100)/total);

	OSATaskSleep(20);
	CPUartLogPrintf("writen %d  total %d  progress %d",writen, total, g_upgrade_fw_status.progress);
}

#if 0
UINT32 verify_flash_fbf_file(UINT32 flash_addr, UINT32 file_size)
{
	char *buf = NULL, *head_buf = NULL, *rsa_buf = NULL;
	int n_blk = 0, n_read = 0, ret = UPGRADE_NO_ERROR;
	UINT32 has_rsa = 0, bytes_read = 0, total_read = 0;
	UINT32 image_padding, image_length, image_id;
	UINT32 rsa_image_len = 0;
	UINT32 BLOCK_SIZE = get_flash_block_size();
	ImginfoTable image_table = {0};
    ImginfoTable *pimage_table = &image_table;
	mbedtls_sha1_context hash_ctx;
	UINT8 hash_out[20] = {0};

	Duster_module_Printf("%s:flash addr 0x%0x, file size 0x%0x",__FUNCTION__, flash_addr, file_size);
	if(file_size < BLOCK_SIZE)
	{
		ret = UPGRADE_ERR_IMAGE_SIZE;
		CPUartLogPrintf("%s[ERROR]:wrong image size 0x%x", __FUNCTION__, file_size);
		goto end;
	}

	buf = extMalloc(2 * BLOCK_SIZE);
	if (!buf)
	{
		ret = UPGRADE_ERR_MEMORY;
		goto end;
	}

	memset(buf, 0, BLOCK_SIZE);

	ret = ReadFlash(flash_addr, (UINT_T)buf, BLOCK_SIZE, BOOT_FLASH);
	if (ret != UPGRADE_NO_ERROR) {
		ret = UPGRADE_ERR_IO;
		CPUartLogPrintf("%s[ERROR]:read failed!", __FUNCTION__);
	}
	n_blk++;
	ret = fbf_parse(buf, &image_table, &has_rsa);
	if (ret != UPGRADE_NO_ERROR) {
		ret = UPGRADE_ERR_INVALID_IMAGE;
		CPUartLogPrintf("%s[ERROR]:fbf parse failed!", __FUNCTION__);
	}
	total_read = BLOCK_SIZE;
	bytes_read = BLOCK_SIZE;
	if(!has_rsa)
	{
		CPUartLogPrintf("%s[ERROR]:not find rsa image!", __FUNCTION__);
		ret = UPGRADE_ERR_NOT_SIGNATURE;
		goto end;
	}

	while(pimage_table != NULL)
	{
		image_padding = 0;
       	 image_length = pimage_table->Img_Len;
		image_id = pimage_table->Img_ID;

		if(image_length == 0)
	        {
	            CPUartLogPrintf("%s: image is empty, read next",__FUNCTION__);
	            pimage_table = pimage_table->next;
	            continue;
	        }

		if(image_length%flash_sector_size > 0)
             		image_padding = flash_sector_size - image_length%flash_sector_size;

     		  image_length = image_length + image_padding;

		CPUartLogPrintf("%s padding image size 0x%x", __FUNCTION__,image_length);
		if (image_length > MAX_IMAGE_BUFFER)
		{
		     CPUartLogPrintf("%s[ERROR]: wrong parsed image size 0x%x",__FUNCTION__, image_length);
	             ret = UPGRADE_ERR_IMAGE_SIZE;
	             goto end;
		}
		if(image_id == RSA_IMAGE_ID)
		{
			rsa_image_len = pimage_table->Img_Len;
			Duster_module_Printf("%s: rsa image len %u",__FUNCTION__,rsa_image_len);
			if (rsa_buf == NULL)
				rsa_buf = malloc(rsa_image_len);

			if(rsa_buf == NULL)
			{
				Duster_module_Printf("%s[ERROR]: malloc image len %u failed",__FUNCTION__, rsa_image_len);
				ret = UPGRADE_ERR_MEMORY;
				goto end;
			}
			memset(rsa_buf, 0, rsa_image_len);
		}
		pimage_table = pimage_table->next;
	}

	if(has_rsa)
	{
		fbf_hash_start(&hash_ctx);
		fbf_hash_update(&hash_ctx, (unsigned char *)buf, bytes_read);

		while(total_read < file_size)
		{
			bytes_read = (file_size - total_read)/BLOCK_SIZE > 0 ? BLOCK_SIZE:(file_size - total_read)%BLOCK_SIZE;
			ret = ReadFlash(flash_addr + n_blk*BLOCK_SIZE, (UINT_T)buf, bytes_read, BOOT_FLASH);
			if (ret != UPGRADE_NO_ERROR) {
				ret = UPGRADE_ERR_IO;
				CPUartLogPrintf("%s[ERROR]:read failed!", __FUNCTION__);
			}
			n_blk++;
			total_read += bytes_read;
			Duster_module_Printf("%s: bytes_read 0x%x total_read 0x%x", __FUNCTION__, bytes_read, total_read);
			if(total_read == file_size)
			{
				bytes_read -= flash_sector_size;
				memcpy(rsa_buf, buf + bytes_read, rsa_image_len);
				Duster_module_Printf("%s: rsa image len 0x%x", __FUNCTION__, rsa_image_len);
				fbf_hash_update(&hash_ctx, (unsigned char *)buf, bytes_read);
				fbf_hash_out(&hash_ctx, hash_out);
				if((ret = rsa_verify((unsigned char *)rsa_buf, rsa_image_len, (char *)hash_out, 20)) != UPGRADE_NO_ERROR)
				{
					CPUartLogPrintf("%s:[Error] signature check failed %d", __FUNCTION__, ret);
					ret = UPGRADE_ERR_NOT_SIGNATURE;
				}
				goto end;
			}
			fbf_hash_update(&hash_ctx, (unsigned char *)buf, bytes_read);
		}
	}
	ret = UPGRADE_ERR_NOT_SIGNATURE;
end:
	if(buf)
		free(buf);

	if(rsa_buf)
		free(rsa_buf);

	Duster_module_Printf("%s:leave %d",__FUNCTION__, ret);
	return ret;
}
#endif

UINT32 upgrade_flash_fbf_file(UINT32 flash_addr)
{

	char *buf = NULL, *head_buf = NULL, *image_start = NULL;
	int n_blk = 0, n_read = 0, ret = UPGRADE_NO_ERROR;
	UINT32 has_rsa = 0, bytes_read = 0, total_read = 0, image_read = 0;
	UINT32 image_padding, image_length, image_id;
	UINT32 rsa_image_len;
	UINT32 BLOCK_SIZE = get_flash_block_size();
	ImginfoTable image_table = {0};
	ImginfoTable *pimage_table = &image_table;
	mbedtls_sha1_context hash_ctx;
	UINT8 hash_out[20] = {0};

	buf = extMalloc(2*BLOCK_SIZE);
	if (!buf)
	{
		ret = UPGRADE_ERR_MEMORY;
		goto end;
	}

	memset(buf, 0, 2*BLOCK_SIZE);

	ret = ReadFlash(flash_addr, (UINT_T)buf, BLOCK_SIZE, BOOT_FLASH);
	if (ret != UPGRADE_NO_ERROR) {
		ret = UPGRADE_ERR_IO;
		CPUartLogPrintf("%s[ERROR]:read failed!", __FUNCTION__);
	}
	n_blk++;
	ret = fbf_parse(buf, &image_table, &has_rsa);
	if (ret != UPGRADE_NO_ERROR) {
		ret = UPGRADE_ERR_INVALID_IMAGE;
		CPUartLogPrintf("%s[ERROR]:fbf parse failed!", __FUNCTION__);
	}
	bytes_read = BLOCK_SIZE;
	image_read = BLOCK_SIZE - FBF_FILE_HEAD_SIZE;
	memmove(buf, buf + FBF_FILE_HEAD_SIZE, image_read);
	image_start = buf;
	while(pimage_table != NULL)
	{
		image_padding = 0;
        image_length = pimage_table->Img_Len;
		image_id = pimage_table->Img_ID;

		if(image_length == 0)
        {
            CPUartLogPrintf("%s: image is empty, read next",__FUNCTION__);
            pimage_table = pimage_table->next;
            continue;
        }

		if(image_length%flash_sector_size > 0)
             image_padding = flash_sector_size - image_length%flash_sector_size;
        image_length = image_length + image_padding;

		CPUartLogPrintf("%s padding image length 0x%x, image read 0x%x", __FUNCTION__,image_length, image_read);
		if (image_length > MAX_IMAGE_BUFFER)
		{
			CPUartLogPrintf("%s[ERROR]: wrong parsed image size 0x%x",__FUNCTION__, image_length);
            ret = UPGRADE_ERR_IMAGE_SIZE;
            goto end;
		}

		if(image_id == RSA_IMAGE_ID)
		{
			rsa_image_len = pimage_table->Img_Len;
			CPUartLogPrintf("%s: rsa image len %u",__FUNCTION__,rsa_image_len);
		}
		else
		{
			while(image_length > image_read)
			{
				//read next block
				ret = ReadFlash(flash_addr + n_blk*BLOCK_SIZE, (UINT_T)(buf + image_read), BLOCK_SIZE, BOOT_FLASH);
				if (ret != UPGRADE_NO_ERROR) {
					ret = UPGRADE_ERR_IO;
					CPUartLogPrintf("%s[ERROR]:read failed!", __FUNCTION__);
					goto end;
				}
				n_blk++;
				//image_start = buf + FBF_FILE_HEAD_SIZE;
				image_read += BLOCK_SIZE;
			}

			CPUartLogPrintf("%s:image start:0x%x, image read 0x%x", __FUNCTION__, image_start, image_read);
			ret = image_valid_check(pimage_table, (UINT32)image_start);
	        if(ret !=  UPGRADE_NO_ERROR)
	        {
				CPUartLogPrintf("%s[ERROR]:image invalid!", __FUNCTION__);
				ret = UPGRADE_ERR_INVALID_IMAGE;
				goto end;
	        }
			if((ret = fbf_writeflash2(pimage_table, (UINT_T)image_start)) != UPGRADE_NO_ERROR)
	        {
				CPUartLogPrintf("%s[ERROR]:read failed!", __FUNCTION__);
				ret = UPGRADE_ERR_IO;
				goto end;
	        }
			image_read -= image_length;
			CPUartLogPrintf("%s:after install, image read:0x%x", __FUNCTION__,image_read);
			memmove(buf, image_start + image_length,  image_read);

			image_start = buf;
			CPUartLogPrintf("%s:image size 0x%x, flash addr 0x%x",__FUNCTION__,image_length, pimage_table->Flash_Start_Address);

		}
		pimage_table = pimage_table->next;
	}

end:
	if(buf)
		free(buf);

	CPUartLogPrintf("%s:leave code %d", __FUNCTION__, ret);
	return ret;
}

UINT32 upgrade_ram_fbf_file(s_fbf_buf_ctx *pctx)
{
#if !defined(CRANE_WEBUI_SUPPORT)
	UINT32 ret = NoError;
	UINT32 has_rsa = 0;
    ImginfoTable image_table = {0};
    ImginfoTable *pimage_table = &image_table;
	UINT32 bytes_read = 0, image_padding, image_length, image_id, total_size = 0;
	UINT32 Flash_Start_Address;
	char *image_buffer = NULL;
	UINT32 len, len2;
	char *temp_buf = NULL;

	ret = fbf_parse(pctx->buffer_start, &image_table, &has_rsa);
	bytes_read += FBF_FILE_HEAD_SIZE;

	if(has_rsa)
	{
		 total_size = pctx->bytes_receive - flash_sector_size;
	}
	else
		 total_size = pctx->bytes_receive;

	if(ret != 0)
	{
		return ret;
	}
	set_upgrade_status(2);// upgrading

	while(pimage_table != NULL)
    {
        image_padding = 0;
        image_length = pimage_table->Img_Len;
		image_id = pimage_table->Img_ID;

        if(image_length == 0)
        {
            CPUartLogPrintf("image is empty, read next");
            pimage_table = pimage_table->next;
            continue;
        }

        if(image_length%flash_sector_size > 0)
             image_padding = flash_sector_size - image_length%flash_sector_size;
        image_length = image_length + image_padding;

		CPUartLogPrintf("%s padding image size 0x%x", __FUNCTION__,image_length);
        Flash_Start_Address = pimage_table->Flash_Start_Address;

        if(image_length > MAX_IMAGE_BUFFER)
        {
            CPUartLogPrintf("wrong parsed image size");
			ret = GeneralError;
            goto END;
        }

		if(bytes_read >= (pctx->buffer_len + pctx->buffer2_len))
		{// buffer3
			len2 = bytes_read - (pctx->buffer_len + pctx->buffer2_len);
			image_buffer = (char *)pctx->buffer3_start + len2;
		}
		else if((bytes_read + image_length >  (pctx->buffer_len + pctx->buffer2_len))
			 && (bytes_read <	(pctx->buffer_len + pctx->buffer2_len)))
		{// buffer2 & buffer3
			len = (pctx->buffer_len + pctx->buffer2_len) - bytes_read;
			len2 = image_length - len;
			if(image_length > MAX_IMAGE_BUFFER)
			{
				CPUartLogPrintf("%s: invalid image size %x",__FUNCTION__, image_length);
				ret = GeneralError;
				goto END;
			}
			temp_buf = extMalloc(MAX_IMAGE_BUFFER);
			if(temp_buf == NULL)
			{
				CPUartLogPrintf("%s: malloc size %x failed",__FUNCTION__, MAX_IMAGE_BUFFER);
				ret = GeneralError;
				goto END;
			}
			memset(temp_buf, 0, MAX_IMAGE_BUFFER);
			memcpy(temp_buf, pctx->buffer2_start + bytes_read - pctx->buffer_len, len);
			memcpy(temp_buf + len, pctx->buffer3_start, len2);
			image_buffer = temp_buf;
		}
		else if((bytes_read + image_length <=  (pctx->buffer_len + pctx->buffer2_len))
			 && (bytes_read >=  pctx->buffer_len))
		{// buffer2
			len2 = bytes_read - pctx->buffer_len;
			image_buffer = (char *)pctx->buffer2_start + len2;
		}
		else if(bytes_read + image_length <= pctx->buffer_len)
		{
			//buffer1
			image_buffer = (char *)pctx->buffer_start + bytes_read;
		}
		else
		{// buffer1 & buffer1
			len = pctx->buffer_len - bytes_read;
			len2 = image_length - len;
			if(image_length > MAX_IMAGE_BUFFER)
			{
				CPUartLogPrintf("%s: invalid image size %x",__FUNCTION__, image_length);
				ret = GeneralError;
	            goto END;
			}
			temp_buf = extMalloc(MAX_IMAGE_BUFFER);
			if(temp_buf == NULL)
			{
				CPUartLogPrintf("%s: malloc size %x failed",__FUNCTION__, MAX_IMAGE_BUFFER);
				ret = GeneralError;
	            goto END;
			}
			memset(temp_buf, 0, MAX_IMAGE_BUFFER);

			memcpy(temp_buf, pctx->buffer_start + bytes_read, len);
			memcpy(temp_buf + len, pctx->buffer2_start, len2);
			image_buffer = temp_buf;
		}


		bytes_read += image_length;

		if(image_id != RSA_IMAGE_ID)
		{
			ret = image_valid_check(pimage_table, (UINT32)image_buffer);
	        if(ret !=  NoError)
	        {
	            CPUartLogPrintf("image invalid");
				ret = GeneralError;
				goto END;
	        }
			set_upgrade_progress(bytes_read, total_size);
			if((ret = fbf_writeflash2(pimage_table, (UINT_T)image_buffer)) != NoError)
	        {
	            CPUartLogPrintf("Write image failed");
				ret = WriteError;
				goto END;
	        }

			if(temp_buf)
			{
				free(temp_buf);
				temp_buf = NULL;

			}
		}

        CPUartLogPrintf("write image size 0x%x successfully", image_length);
        pimage_table = pimage_table->next;
    }

END:
	if(temp_buf)
	{
		free(temp_buf);
		temp_buf = NULL;
	}
	return ret;
#else
	return 0;
#endif
}

int modify_xml_get_file_nucleus(char *old_file, char *new_file)
{
	char command[2 * PATH_MAX];
	char buffer[4096];
	FILE_ID fid1, fid2;
    int read_size,write_size;

	//snprintf(command, 2 * PATH_MAX, "/bin/cp -f %s %s", old_file, new_file);
    if((fid1 = Rdisk_fopen(old_file)) <= 0 )
    {
            cgi_err("open %s file failed", old_file);
			return -1;
	}
	if((fid2 = Rdisk_fopen(new_file)) <= 0 )
	{
	        cgi_err("open %s file failed", new_file);
			return -1;
    }
	read_size = Rdisk_fread(buffer,sizeof(char), 4096, fid1);
	cgi_log("modify_xml_get_file_nucleus: read %s size is: %d",old_file, read_size);
	write_size = Rdisk_fwrite(buffer,sizeof(char), read_size, fid2);
	cgi_log("modify_xml_get_file_nucleus: write %s size is: %d", new_file, write_size);
	assert(read_size == write_size);
	Rdisk_fclose(fid1);
	Rdisk_fclose(fid2);
	return 0;
}


int  cgi_duster_parse(char *getsetrestore, char *filename, char *xml_res)
{
    if (!strcasecmp(getsetrestore, "get"))
    {
        cgi_log("=== LOG4: 进入duster GET处理 ===");
        cgi_log("=== LOG4: 文件路径: %s ===", filename);
        return duster_parser_nucleus("get", filename, xml_res);
    }
    else if (!strcasecmp(getsetrestore, "restore"))
    {
       cgi_log("Before duster restore handler");
       return duster_parser_nucleus("restore", filename, xml_res);
    }
    else
    {
       cgi_log("Before duster set handler");
       return duster_parser_nucleus("set", filename, xml_res);
    }
}


int erase_psm_block(char erase_block_num)
{
	char i =0;
	if(erase_block_num > (char) TOTAL_PSMFILE_NUM)
		 erase_block_num  = (char) TOTAL_PSMFILE_NUM;
	for(i=0;i<erase_block_num;i++)
	{
		cgi_log("%s: erase psm %d block data",__FUNCTION__,i);
		psm_eraseall__(i);
		psm_close(g_handle[i], i);
	}
	return 0;
}
void set_system_in_upgrade_status(void)
{
	sw_update_flag = 1;
	g_session_time = 3000000;// enough large time
}

void clear_system_in_upgrade_status(void)
{
	sw_update_flag = 0;
}


int is_system_in_upgrade_status(void)
{
	return sw_update_flag;
}

void set_full_system_ram_flag(void)
{
#ifndef CRANE_WEBUI_SUPPORT
	*(volatile UINT32 *)(DDR_MIN_MAX_SYSTEM_FLAG_ADDR) = (UINT32)(0x4D415853);
#endif
}

void clear_full_system_ram_flag(void)
{
#ifndef CRANE_WEBUI_SUPPORT
	*(volatile UINT32 *)(DDR_MIN_MAX_SYSTEM_FLAG_ADDR) = 0;
#endif
}

void set_mini_system_ram_flag(void)
{
#ifndef CRANE_WEBUI_SUPPORT
	*(volatile UINT32 *)(DDR_MIN_MAX_SYSTEM_FLAG_ADDR) = (UINT32)(0x4D494E53);
#endif
}


void set_system_in_upgrade_flag(void)
{
#ifndef CRANE_WEBUI_SUPPORT
	*(volatile UINT32 *)(UPGRADE_FLAG_ADDR_BASE) = (UINT32)(0x12344321);
#endif
}


void CheckUpgradeReset(void)
{
#if 0
	if(0x12344321 == (*((volatile UINT32 *)(UPGRADE_FLAG_ADDR_BASE ))))
	{
		*(volatile UINT32 *)(UPGRADE_FLAG_ADDR_BASE) = 0;
		InUpgradeReset = TRUE;
		CPUartLogPrintf("%s: In Upgrade Reset",__FUNCTION__);
	}
	else
		InUpgradeReset = FALSE;
#else
	InUpgradeReset = FALSE;
#endif
}

int check_fbf_version_validation(void * img_addr)
{
	char fbf_version[16] = {0};

	if(get_fbf_version(img_addr, fbf_version) == 0)
	{
		CPUartLogPrintf("%s: fbf_version %s",__FUNCTION__, fbf_version);
		if(is_fbf_version_valid(fbf_version))
		{
			CPUartLogPrintf("%s: FBF version valid, allow upgrade",__FUNCTION__);
			return 1;
		}
	}

	return 0;
}

int erase_file_system(UINT32 start_address, UINT32 erase_size)
{
	UINT32 EraseSize;
	UINT32 Retval;
	if(erase_size%0x20000 == 0)
	    EraseSize = erase_size;
	else
	    EraseSize = (erase_size/0x20000 + 1) * 0x20000;

    SetFlashProtectStatus(0);

	Retval = EraseFlash(start_address,
							EraseSize,
							BOOT_FLASH);
	if(Retval != NoError)
	{
	    SetFlashProtectStatus(1);
		CPUartLogPrintf("%s: Erash flash failed %d",__FUNCTION__, Retval);
		return Retval;
	}
	return 0;

}

bool support_ddr_upgrade(void)
{

	UINT32 mode_ver = get_platform_mode_version();
	if((mode_ver == PLATFORM_3MODE_LTG_VER || mode_ver == PLATFORM_3MODE_LWG_VER) || PlatformIsZmifi())
	{
		return TRUE;
	}
	else
		return FALSE;
}

BOOL support_unify_upgrade(void)
{
#if defined(PLAT_FALCON) || defined(PLAT_LAPWING)
    return TRUE;
#else
    return FALSE;
#endif
}
bool support_fail_safe_upgrade(void)
{
	PlatformBoardType board_type = PlatformGetBoardType();

	if(support_ddr_upgrade())
		return FALSE;

	if((board_type == NEZHA_MMIFI_V3_BOARD) && !PlatformIsZmifi())
		return TRUE;
	else
		return FALSE;
}


int fail_safe_upgrade(char *content_length, char *content_type)
{
#define flash_sector_size 8192
		char fromservermsg_buf[SERVER_TO_CGI_MESSAGE_Q_SIZE];
		OSA_STATUS osa_status;
		struct http_cgi_msg *pfromservermessage;
		int bytes_read = 0, file_size = 0;
		int bodyLen = 0;
		ImginfoTable image_table;
		ImginfoTable *pimage_table = &image_table;
		int fbf_return =0 ;
		int image_length;
		int Flash_Start_Address;
		char *image_buffer = NULL;
		char *image_buffer_dynamic = NULL;
		struct cgi_http_msg toservermessage;
		int image_padding = 0;
		int image_index = 0;
		int bytes_store = 0, bytes_writen;
		int ret = ERR_NEED_OBM_UPGRAGE_OK;
		UINT32 has_rsa = 0;
		UINT32 BLOCK_SIZE = get_flash_block_size();

		bodyLen = atoi(content_length);
		if(bodyLen > MAX_UPGRADE_SIZE)
		{
			CPUartLogPrintf("%s:[ERROR]File size %d is bigger than Max size %d :upgrade is rejected",__FUNCTION__, bodyLen,MAX_UPGRADE_SIZE);
			ret = ERR_NEED_OBM_UPGRAGE_FILE_SIZE;
			goto done;
		}
		 memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);
				/* Read the body of the POST request using msg_queue */
		 osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
		 assert(osa_status == OS_SUCCESS);
		 pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;
		 CPUartLogPrintf("%s: enter", __FUNCTION__);
		 CPUartLogPrintf("Reced upgrade POST body data msg id is: %x, length is %d",pfromservermessage->msgid,pfromservermessage->length);
		 if( pfromservermessage->msgid == 0x55 )
		 {
		 	if( pfromservermessage->length > 0 )
		 	{
		 		if( pfromservermessage->length == FBF_FILE_HEAD_SIZE)
		 		{
		 			#ifdef TR069_SUPPORT
		 			if(cpe.upgrades_managed != 0)
					{
						CPUartLogPrintf("%s:[ERROR] Not allow WebUI upgrade, set by ACS",__FUNCTION__);
						ret = ERR_NEED_OBM_UPGRAGE_NOT_ALLOW;
						goto done;
					}
					#endif
		 			memset(recv_buf, 0, sizeof(recv_buf));
					memcpy(recv_buf, pfromservermessage->data, pfromservermessage->length);
					recv_buf[pfromservermessage->length] = '\0';
					fbf_return = fbf_parse(recv_buf, &image_table, &has_rsa);
					if(fbf_return != 0)
					{
						CPUartLogPrintf("%s:[ERROR] fbf_parse FAILED!", __FUNCTION__);
						ret = ERR_NEED_OBM_UPGRAGE_FILE_INVALID;
						goto done;
					}

					image_buffer = extMalloc(MAX_IMAGE_BUFFER + FBF_FILE_HEAD_SIZE);
					if(image_buffer == NULL)
					{
						CPUartLogPrintf("%s:[ERROR] malloc image buffer FAILED!", __FUNCTION__);
						ret = ERR_NEED_OBM_UPGRAGE_MEM;
						goto done;
					}

					//copy FBF head into image_buffer
					memcpy(image_buffer, recv_buf, FBF_FILE_HEAD_SIZE);
					image_buffer_dynamic = image_buffer + FBF_FILE_HEAD_SIZE;
					image_index = 0;
					bytes_store = FBF_FILE_HEAD_SIZE;
					while(pimage_table != NULL)
					{
						bytes_read = 0;
						image_padding = 0;
						image_length = pimage_table->Img_Len;
						CPUartLogPrintf("before padding, image size is %d", image_length);
						if(image_length == 0)
						{
							CPUartLogPrintf("image is empty, read next");
							pimage_table = pimage_table->next;
							continue;//
						}

						if(image_length%flash_sector_size > 0)
							 image_padding = flash_sector_size - image_length%flash_sector_size;
						image_length = image_length + image_padding;
						Flash_Start_Address = pimage_table->Flash_Start_Address;
						if(image_length > MAX_IMAGE_BUFFER)
						{
							CPUartLogPrintf("%s:[ERROR]wrong parsed image size 0x%x",__FUNCTION__,image_length);
							ret = ERR_NEED_OBM_UPGRAGE_FILE_INVALID;
							goto done;
						}
						toservermessage.msgid = 0x77;
						toservermessage.image_length = image_length;
						osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
						while(bytes_read < image_length)
						{
							 memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);
							  /* Read the body of the POST request using msg_queue */
							 osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
							 assert(osa_status == OS_SUCCESS);
							 pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;

							 if( pfromservermessage->msgid == 0x55 )
							 {
								if( pfromservermessage->length > 0 )
								{
									 if(bytes_read + pfromservermessage->length > MAX_IMAGE_BUFFER)
										  pfromservermessage->length = (int)(MAX_IMAGE_BUFFER - bytes_read);
									 memcpy(image_buffer_dynamic, pfromservermessage->data, pfromservermessage->length);
									 bytes_read += pfromservermessage->length;
									 image_buffer_dynamic += pfromservermessage->length;//update buffer pointer
									 duster_free(pfromservermessage->data);//free buffer malloced in mongoose
								}
							 }
							 else if(pfromservermessage->msgid == 0xaa)
							 {
							 	Duster_module_Printf(1, "%s:[ERROR] download firmware abort",__FUNCTION__);
								ret = ERR_NEED_OBM_UPGRAGE_HTTP;
								goto done;
							 }
							// CPUartLogPrintf("recvd image size is %d, total recvd image size is %d", pfromservermessage->length, bytes_read);
						}
						CPUartLogPrintf("read image successfully, now check validation...");
						if(fbf_imagecheck(pimage_table, (UINT_T)(image_buffer_dynamic - image_length)))
						{
							CPUartLogPrintf("%s:[ERROR] image validation FAILED!",__FUNCTION__);
							ret = ERR_NEED_OBM_UPGRAGE_FILE_INVALID;
							goto done;
						}
						CPUartLogPrintf("validation OK, now write to flash...");

						// first collect image size to a flash block
						bytes_store += image_length;

	check_point:		if(bytes_store < BLOCK_SIZE)
						{
							// need read more image
							CPUartLogPrintf("need read more image to collect to a flash block");
							image_buffer_dynamic = image_buffer + bytes_store;


						}
						else
						{
							bytes_writen = BLOCK_SIZE;
							if(burn_fbf_file_2flash(image_index, bytes_writen, (UINT32)image_buffer))
							{
								CPUartLogPrintf("%s:[ERROR] write image to flash FAILED!",__FUNCTION__);
								ret = ERR_NEED_OBM_UPGRAGE_FLASHIO;
								goto done;
							}
							image_index++;
							file_size += bytes_writen;
							if(bytes_store > BLOCK_SIZE)
							{
								memcpy(image_buffer, image_buffer + BLOCK_SIZE, bytes_store - BLOCK_SIZE);
								image_buffer_dynamic = image_buffer + bytes_store - BLOCK_SIZE;
							}
							else
								image_buffer_dynamic = image_buffer;

							bytes_store -= bytes_writen;
							//here we should check again
							goto check_point;
						}
						pimage_table = pimage_table->next;
						if(pimage_table == NULL)
						{
							CPUartLogPrintf("write the end of fbf to flash!");
							bytes_writen = bytes_store;
							if(burn_fbf_file_2flash(image_index, bytes_writen, (UINT32)image_buffer))
							{
								CPUartLogPrintf("%s:[ERROR] write image to flash FAILED!",__FUNCTION__);
								ret = ERR_NEED_OBM_UPGRAGE_FLASHIO;
								goto done;
							}
							image_index++;
							file_size += bytes_writen;
							bytes_store -= bytes_writen;
						}
					}
					toservermessage.msgid = 0x99;
					toservermessage.image_length = 0;
					osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
		 		}
		 	}
		 }
 done:
	 if(image_buffer)
	 {
		 free(image_buffer);
	 }
	 free_fbf_image_list(&image_table);
	 Duster_module_Printf(3, "%s: return %d", __FUNCTION__, ret);
 	 if(ret != ERR_NEED_OBM_UPGRAGE_OK)
  	 {
  	 	// trigger server to do not download file anymore, just close the socket
	    toservermessage.msgid = 0xaa;
	    toservermessage.image_length = 0;
	    osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);

  	 	return ret;
 	 }

	 if(set_obm_upgrade_flag(file_size)!= ERR_NEED_OBM_UPGRAGE_OK)
	 {
		Duster_module_Printf(1, "%s:[Fatal Error] notify OBM flag FAILED!",__FUNCTION__);
		ret = ERR_NEED_OBM_UPGRAGE_FLASHIO;
	 }

	 notify_server_download_firmware_done();
	 return ret;
}

int set_obm_upgrade_flag(int file_size)
{
	int ret = ERR_NEED_OBM_UPGRAGE_OK;
#ifdef PLAT_FALCON
	UINT32 BLOCK_SIZE = get_flash_block_size();
	FlashLayoutConfInfo * flash_layout = GetFlashLayoutConfig();
	char *temp = NULL;

	temp = extMalloc(BLOCK_SIZE);
	if(!temp)
	{
		CPUartLogPrintf("%s: [ERROR] malloc size 0x%x FAILED!", __FUNCTION__, BLOCK_SIZE);
	}
	else
	{
		s_sd_firmware_flag *flag = (s_sd_firmware_flag *)temp;
		memset(temp, 0, BLOCK_SIZE);
		flag->header = 0x54524657; // TRFW
		flag->upgrade_flag = 1;
		flag->fbf_flash_address = (BLOCK_SIZE + flash_layout->FBFStartAddress);
		flag->fbf_file_size = (unsigned int)file_size;
		Duster_module_Printf(3, "%s:fbf_flash_address 0x%08x, fbf_file_size %d", __FUNCTION__, flag->fbf_flash_address, flag->fbf_file_size);
		ret = EraseFlash(flash_layout->FBFStartAddress, BLOCK_SIZE, BOOT_FLASH);
		if(ret != NoError)
		{
			CPUartLogPrintf("%s: [ERROR] erase FAILED!", __FUNCTION__);
			ret = ERR_NEED_OBM_UPGRAGE_FLASHIO;
		}
		ret = WriteFlash(flash_layout->FBFStartAddress, (UINT_T)temp, BLOCK_SIZE, BOOT_FLASH);
		if (ret != NoError) {
			CPUartLogPrintf("%s: [ERROR] write FAILED!", __FUNCTION__);
			ret = ERR_NEED_OBM_UPGRAGE_FLASHIO;
			// If flash write faile, How proceess this case? Need re-erase avoid the data not complete?
			EraseFlash(flash_layout->FBFStartAddress, BLOCK_SIZE, BOOT_FLASH);
		}
		// Read flag and show it
		memset(temp, 0, BLOCK_SIZE);
		ret = ReadFlash(flash_layout->FBFStartAddress, (UINT_T)temp, BLOCK_SIZE, BOOT_FLASH);
		if (ret != NoError) {
			printf(" set flag: read failed");
			CPUartLogPrintf("%s: [ERROR] read FAILED!", __FUNCTION__);
			ret = ERR_NEED_OBM_UPGRAGE_FLASHIO;
		} else {
			CPUartLogPrintf("%s:Flag header: %08x",__FUNCTION__,flag->header);
			CPUartLogPrintf("%s:Flag upgrade: %08x", __FUNCTION__, flag->upgrade_flag);
			CPUartLogPrintf("%s:Flag fbf flash address: %08x", __FUNCTION__, flag->fbf_flash_address);
			CPUartLogPrintf("%s:Flag fbf file size: %d", __FUNCTION__, flag->fbf_file_size);
			CPUartLogPrintf("%s:Flag erase psm: %08x", __FUNCTION__, flag->erase_psm);
			CPUartLogPrintf("%s:Flag erase psm address: %08x", __FUNCTION__, flag->erase_psm_address);
			CPUartLogPrintf("%s:Flag erase psm size: %08x", __FUNCTION__, flag->erase_psm_size);
			ret = ERR_NEED_OBM_UPGRAGE_OK;
		}
		free(temp);
		temp = NULL;
	}
#endif
	return ret;
}

enum download_msg_flag
{
    DOWNLOAD_MSG_START,
    DOWNLOAD_MSG_END,
    DOWNLOAD_MSG_ABORT
};
int send_download_msg(int next_image_len, enum download_msg_flag flag)
{
    struct cgi_http_msg toservermessage = { 0 };
    OSA_STATUS osa_status;
    if (flag == DOWNLOAD_MSG_START)
    {
        toservermessage.msgid = 0x77;
        toservermessage.image_length = next_image_len;
    }
    else if (flag == DOWNLOAD_MSG_END)
    {
        toservermessage.msgid = 0x99;
        toservermessage.image_length = 0;
    }
    else if (flag == DOWNLOAD_MSG_ABORT)
    {
        toservermessage.msgid = 0xaa;
        toservermessage.image_length = 0;
    }
    osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
    if (osa_status != OS_SUCCESS)
        return UPGRADE_ERR_SYSTEM;
    return UPGRADE_NO_ERROR;
}

int unify_upgrade_download_and_flash(char *content_length, char *content_type)
{
#if defined(PLAT_FALCON) || defined(PLAT_LAPWING)
    char fromservermsg_buf[SERVER_TO_CGI_MESSAGE_Q_SIZE];
    OSA_STATUS osa_status;
    struct http_cgi_msg *pfromservermessage = NULL;
    int ret = UPGRADE_NO_ERROR;
    int total_body_len = atoi(content_length);
    int consumed_body_len = 0;
    BOOL download_image_done = FALSE;
    int receive_count = 0;
    int single_receive_size = 0;
    CPUartLogPrintf("%s: total_body_len %d", __func__, total_body_len);
    while(consumed_body_len < total_body_len)
    {
        osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
        if (osa_status != OS_SUCCESS)
        {
            CPUartLogPrintf("%s: osa_status %d", __func__, osa_status);
            ret = UPGRADE_ERR_SYSTEM;
            goto end;
        }
        receive_count++;
        pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;
        CPUartLogPrintf("%s: received msg id 0x%x, length %d",__func__, pfromservermessage->msgid,pfromservermessage->length);
        if (pfromservermessage->msgid == MSG_DOWNLOAD_UPGRADE_FILE_ABORT)
        {
            ret = UPGRADE_ERR_OTHER;
            goto end;
        }
        if (pfromservermessage->data == NULL || pfromservermessage->length <= 0)
        {
            ret = UPGRADE_ERR_IO;
            goto end;
        }
        if (fota_pkg_write(pfromservermessage->data, pfromservermessage->length, total_body_len) != UPGRADE_NO_ERROR)
        {
            CPUartLogPrintf("%s: fota_pkg_write data 0x%x length %d failed", __func__, pfromservermessage->data, pfromservermessage->length);
            ret = UPGRADE_ERR_IO;
            goto end;
        }
        single_receive_size += pfromservermessage->length;
        consumed_body_len += pfromservermessage->length;
        CPUartLogPrintf("%s: single_receive_size %d, consumed_body_len %d", __func__, single_receive_size, consumed_body_len);
        if (pfromservermessage->msgid == MSG_DOWNLOAD_UPGRADE_FILE_END)
        {
            CPUartLogPrintf("%s: receive fbf size %d done", __func__, consumed_body_len);
            dump_buffer("last fbf", pfromservermessage->data, pfromservermessage->length);
			duster_free(pfromservermessage->data);
            break;
        }
        if (receive_count == 1)
        {
            ret = send_download_msg(MAX_IMAGE_BUFFER, DOWNLOAD_MSG_START);
            if ( ret != UPGRADE_NO_ERROR)
            {
                goto end;
            }
            single_receive_size = 0;
        }
        else
        {
            duster_free(pfromservermessage->data);
            if (single_receive_size == MAX_IMAGE_BUFFER)
            {
                ret = send_download_msg(MAX_IMAGE_BUFFER, DOWNLOAD_MSG_START);
                if ( ret != UPGRADE_NO_ERROR)
                {
                    goto end;
                }
                single_receive_size = 0;
            }
        }
    }
    ret = send_download_msg(0, DOWNLOAD_MSG_END);
    if ( ret != UPGRADE_NO_ERROR)
    {
        goto end;
    }
    download_image_done = TRUE;
    if (fota_pkg_flush_flash() != UPGRADE_NO_ERROR)
    {
        CPUartLogPrintf("%s: fota_pkg_flush_flash failed", __func__);
        ret = UPGRADE_ERR_IO;
        goto end;
    }
    if (fota_image_verify() != UPGRADE_NO_ERROR)
    {
        CPUartLogPrintf("%s: fota_image_verify failed", __func__);
        ret = UPGRADE_ERR_INVALID_IMAGE;
        goto end;
    }
end:
    if (!download_image_done)
    {
        if (send_download_msg(0, DOWNLOAD_MSG_ABORT) == UPGRADE_ERR_SYSTEM)
            ret = UPGRADE_ERR_SYSTEM;
    }
    CPUartLogPrintf("%s: ret %d", __func__, ret);
    return ret;
#else
    return UPGRADE_ERR_SYSTEM;
#endif
}
int handle_5mod_upgrade_and_flash(char *content_length, char *content_type)
{
		char fromservermsg_buf[SERVER_TO_CGI_MESSAGE_Q_SIZE];
		OSA_STATUS osa_status;
		struct http_cgi_msg *pfromservermessage;
		int bytes_read=0, total_bytes=0;
		int bodyLen = 10000;
		ImginfoTable image_table;
		ImginfoTable *pimage_table = &image_table;
		int fbf_return =0 ;
		int image_length;
		int Flash_Start_Address;
		char *image_buffer = NULL;
		char *image_buffer_dynamic;
		struct cgi_http_msg toservermessage;
		int image_padding = 0;
		int unpadding_image_length = 0;
		UINT32 has_rsa = 0;
		int ret = UPGRADE_NO_ERROR;

		bodyLen = atoi(content_length);
		if(bodyLen > MAX_UPGRADE_SIZE)
		{
				CPUartLogPrintf("File size %d is bigger than Max size %d :upgrade is rejected",bodyLen,MAX_UPGRADE_SIZE);
				ret = UPGRADE_ERR_IMAGE_SIZE;
				goto end;
		}
		 memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);
				/* Read the body of the POST request using msg_queue */
		 osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
		 assert(osa_status == OS_SUCCESS);
		 pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;
		 cgi_log("Reced upgrade POST body data msg id is: %x, length is %d",pfromservermessage->msgid,pfromservermessage->length);
		 if( pfromservermessage->msgid == 0x55 )
		 {
				if( pfromservermessage->length > 0 )
				{
					//memset(buffer,0, XML_GET_BUF_SIZE+1);
					if( pfromservermessage->length == FBF_FILE_HEAD_SIZE)
					{
						memset(recv_buf, 0, sizeof(recv_buf));
						memcpy(recv_buf, pfromservermessage->data, pfromservermessage->length);
						recv_buf[pfromservermessage->length] = '\0';
						fbf_return = fbf_parse(recv_buf, &image_table, &has_rsa);
						if(fbf_return == 0)
						{
							CPUartLogPrintf("fbf_parse successfully");
							image_buffer = extMalloc(MAX_IMAGE_BUFFER);//Malloc 128K bytes from system heap
						  //  CPUartLogPrintf("malloc buffer address is :%x", image_buffer);
							if(image_buffer == NULL)
							{
								CPUartLogPrintf("malloc image buffer failed");
								ret = UPGRADE_ERR_MEMORY;
								goto end;
							}
							 while(pimage_table != NULL)
							{
								image_buffer_dynamic = image_buffer;
								bytes_read = 0;
								image_padding = 0;
								image_length = pimage_table->Img_Len;
								CPUartLogPrintf("before padding, image size is %d", image_length);
								if(image_length == 0)
								{
									CPUartLogPrintf("image is empty, read next");
									pimage_table = pimage_table->next;
									continue;//
								}

								if(image_length%flash_sector_size > 0)
									 image_padding = flash_sector_size - image_length%flash_sector_size;
								image_length = image_length + image_padding;
								Flash_Start_Address = pimage_table->Flash_Start_Address;

								if(image_length > MAX_IMAGE_BUFFER)
								{
									CPUartLogPrintf("wrong parsed image size");
									ret = UPGRADE_ERR_IMAGE_SIZE;
									goto end;
								}
								toservermessage.msgid = 0x77;
								toservermessage.image_length = image_length;
								osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
								//CPUartLogPrintf("send parsed image size is %d", image_length);
								while(bytes_read < image_length)
								{
									 memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);
									  /* Read the body of the POST request using msg_queue */
									 osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
									 assert(osa_status == OS_SUCCESS);
									 pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;

									 if( pfromservermessage->msgid == 0x55 )
									 {
										if( pfromservermessage->length > 0 )
										{
											  //CPUartLogPrintf("xml_action: reced data length is :%d",pfromservermessage->length);
											 if(bytes_read + pfromservermessage->length > MAX_IMAGE_BUFFER)
												  pfromservermessage->length = (int)(MAX_IMAGE_BUFFER - bytes_read);
											 memcpy(image_buffer_dynamic, pfromservermessage->data, pfromservermessage->length);
											 //CPUartLogPrintf("xml_action: head four bytes are :%x,%x,%x,%x",*image_buffer_dynamic,*(image_buffer_dynamic+1),*(image_buffer_dynamic+2),*(image_buffer_dynamic+3));
											 bytes_read += pfromservermessage->length;
											 image_buffer_dynamic += pfromservermessage->length;//update buffer pointer
											 duster_free(pfromservermessage->data);//free buffer malloced in mongoose
										}
									 }
									 else if(pfromservermessage->msgid == 0xaa)
									 {
									 	Duster_module_Printf(1, "%s: download firmware abort",__FUNCTION__);
										ret = UPGRADE_ERR_OTHER;
										goto end;
									 }
									// CPUartLogPrintf("recvd image size is %d, total recvd image size is %d", pfromservermessage->length, bytes_read);
								}
								CPUartLogPrintf("read image successfully, begin write flash");
								unpadding_image_length += image_length;//update reading image length
								if(!fbf_writeflash(pimage_table, (UINT_T)image_buffer))
								{
									CPUartLogPrintf("Write image successfully");
								}

								CPUartLogPrintf("read next image params");
								pimage_table = pimage_table->next;
							}
							 toservermessage.msgid = 0x99;
							 toservermessage.image_length = 0;
							 osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
							 ret = UPGRADE_NO_ERROR;
						}
						else
						{
							ret = UPGRADE_ERR_INVALID_IMAGE;
							goto end;
						}
					}

				}
				}

end:
	if(image_buffer)
	{
		free(image_buffer);
	}
	free_fbf_image_list(&image_table);

	return ret;
}

int upgrade_and_flash(char *content_length, char *content_type)
{
    char fromservermsg_buf[SERVER_TO_CGI_MESSAGE_Q_SIZE];
    OSA_STATUS osa_status;
    struct http_cgi_msg *pfromservermessage;
    int bytes_read=0, total_bytes=0;
    int bodyLen = 10000;
    ImginfoTable image_table = {0};
    ImginfoTable *pimage_table = &image_table;
    int fbf_return =0 ;
    int image_length;
    int Flash_Start_Address;
    char *image_buffer = NULL;
    char *image_buffer_dynamic = NULL;
	char *rsa_image_buf = NULL;
    struct cgi_http_msg toservermessage;
    int image_padding = 0;
    int padding_image_length = 0;
	int ret = 0;
	UINT32 has_rsa = 0, image_id = 0, rsa_image_len = 0;
	s_fbf_buf_ctx ctx;
	mbedtls_sha1_context hash_ctx;
	UINT8 hash_out[20] = {0};
	UINT32 modever = 0;
    bodyLen = atoi(content_length);

	CPUartLogPrintf("%s: board type %d, support_unify_upgrade %d", __FUNCTION__, PlatformGetBoardType(), support_unify_upgrade());
    if (support_unify_upgrade())
        return unify_upgrade_download_and_flash(content_length, content_type);

#if 0
	if(support_fail_safe_upgrade())
	{
		return fail_safe_upgrade(content_length, content_type);
	}

    modever = get_platform_mode_version();
	CPUartLogPrintf("%s: support_ddr_upgrade 0x%x", __FUNCTION__, modever);

	if (!support_ddr_upgrade())
	{
		CPUartLogPrintf("%s: handle 5mode upgrade", __FUNCTION__);
		return handle_5mod_upgrade_and_flash(content_length, content_type);
	}

	CPUartLogPrintf("%s: handle 3mode upgrade", __FUNCTION__);
    memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);

	/* Read the body of the POST request using msg_queue */
	osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
	assert(osa_status == OS_SUCCESS);

	 // 3mode upgrade
    if(bodyLen > ddr_msa_ir_buffer_size + ddr_dsp_ro_size + ddr_ps_noncache_data_size)
    {
        	CPUartLogPrintf("File size %d is bigger than Max size %d :upgrade is rejected",bodyLen, ddr_msa_ir_buffer_size + ddr_dsp_ro_size + ddr_ps_noncache_data_size);
			set_upgrade_fail_cause(UPGRADE_ERR_IMAGE_SIZE);
			ret = -3;
        	goto ERROR_EXIT;
    }
     pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;
	 cgi_log("Reced upgrade POST body data msg id is: %x, length is %d",pfromservermessage->msgid,pfromservermessage->length);
     if( pfromservermessage->msgid == 0x55 )
	 {
	    if( pfromservermessage->length >= 0 )
	    {
	        //memset(buffer,0, XML_GET_BUF_SIZE+1);
	        if( pfromservermessage->length == FBF_FILE_HEAD_SIZE)
	        {
	            memset(recv_buf, 0, sizeof(recv_buf));
	            memcpy(recv_buf, pfromservermessage->data, pfromservermessage->length);
	            recv_buf[pfromservermessage->length] = '\0';
	            fbf_return = fbf_parse(recv_buf, &image_table, &has_rsa);
	            if(fbf_return == 0)
	            {

	                CPUartLogPrintf("fbf_parse successfully");
					CPUartLogPrintf("has_rsa %d", has_rsa);
					if(PlatformIsTPMifi())
					{
						if(has_rsa == 0)
						{
							CPUartLogPrintf("[FATAL ERROR] Firmware is not signatured, reject to upgrade");
							set_upgrade_fail_cause(UPGRADE_ERR_NOT_SIGNATURE);
							ret = -3;
	                    	goto ERROR_EXIT;
						}
					}
					if(check_fbf_version_validation(recv_buf) == 0)
					{
						CPUartLogPrintf("[FATAL ERROR]FBF verison is invalid to upgrade");
						set_upgrade_fail_cause(UPGRADE_ERR_INVALID_VERSION);
						ret = -3;
	                    goto ERROR_EXIT;
					}
					set_upgrade_status(2);// set in upgrading status

					// store fbf data into buf first
					fbf_buf_ctx_init(&ctx);
					store_data_into_buf(&ctx, (UINT8 *)recv_buf, pfromservermessage->length);

	                image_buffer = extMalloc(MAX_IMAGE_BUFFER);//Malloc 128K bytes from system heap
	                if(image_buffer == NULL)
	                {
	                    CPUartLogPrintf("malloc image buffer failed");
						set_upgrade_fail_cause(UPGRADE_ERR_MEMORY);
						ret = -1;
	                    goto ERROR_EXIT;
	                }

					padding_image_length += FBF_FILE_HEAD_SIZE;
					// only USB interrupt
					//disableIntr_for_fbf();

	        		while(pimage_table != NULL)
	                {
	                    image_buffer_dynamic = image_buffer;
	                    bytes_read = 0;
	                    image_padding = 0;
	                    image_length = pimage_table->Img_Len;
						image_id = pimage_table->Img_ID;

	                    if(image_length == 0)
	                    {
	                        CPUartLogPrintf("image is empty, read next");
	                        pimage_table = pimage_table->next;
	                        continue;//
	                    }

	                    if(image_length%flash_sector_size > 0)
	                         image_padding = flash_sector_size - image_length%flash_sector_size;
	                    image_length = image_length + image_padding;

						CPUartLogPrintf("padding image size 0x%x", image_length);
	                    Flash_Start_Address = pimage_table->Flash_Start_Address;

	                    if(image_length > MAX_IMAGE_BUFFER)
	                    {
	                        CPUartLogPrintf("wrong parsed image size");
							set_upgrade_fail_cause(UPGRADE_ERR_INVALID_IMAGE);
							ret = -1;
	                        goto ERROR_EXIT;
	                    }
	                    toservermessage.msgid = 0x77;
	                    toservermessage.image_length = image_length;
	                    osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
	                    //CPUartLogPrintf("send parsed image size is %d", image_length);
	                    while(bytes_read < image_length)
	                    {
	                         memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);
	                    	  /* Read the body of the POST request using msg_queue */
	                    	 osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
	                    	 assert(osa_status == OS_SUCCESS);
	                    	 pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;

	                    	 if( pfromservermessage->msgid == 0x55 )
	                    	 {
	                    	    if( pfromservermessage->length > 0 )
	                    	    {
	                    	          //CPUartLogPrintf("xml_action: reced data length is :%d",pfromservermessage->length);
	                    	         if(bytes_read + pfromservermessage->length > MAX_IMAGE_BUFFER)
	                    	              pfromservermessage->length = (int)(MAX_IMAGE_BUFFER - bytes_read);
	                    	         memcpy(image_buffer_dynamic, pfromservermessage->data, pfromservermessage->length);
	                    	         //CPUartLogPrintf("xml_action: head four bytes are :%x,%x,%x,%x",*image_buffer_dynamic,*(image_buffer_dynamic+1),*(image_buffer_dynamic+2),*(image_buffer_dynamic+3));
	                    	         bytes_read += pfromservermessage->length;
	                    	         image_buffer_dynamic += pfromservermessage->length;//update buffer pointer
	                    	         duster_free(pfromservermessage->data);//free buffer malloced in mongoose
	                    	    }
	                    	 }
							 else if(pfromservermessage->msgid == 0xaa)
							 {
							 	Duster_module_Printf("%s: download firmware abort",__FUNCTION__);
							 	set_upgrade_fail_cause(UPGRADE_ERR_OTHER);
								ret =  -2;
								goto ERROR_EXIT;
							 }
	                    	// CPUartLogPrintf("recvd image size is %d, total recvd image size is %d", pfromservermessage->length, bytes_read);
	                    }
	                    CPUartLogPrintf("receive image size 0x%x successfully", image_length);
	                    padding_image_length += image_length;//update reading image length

						if(image_id == RSA_IMAGE_ID)
						{
							// no need chech image checksum
							rsa_image_len = pimage_table->Img_Len;
							Duster_module_Printf("%s: rsa image len %u",__FUNCTION__,rsa_image_len);
							if (rsa_image_buf == NULL)
								rsa_image_buf = malloc(rsa_image_len);

							if(rsa_image_buf == NULL)
							{
								Duster_module_Printf("%s: malloc image len %u failed",pimage_table->Img_Len);
								set_upgrade_fail_cause(UPGRADE_ERR_MEMORY);
								ret = -1;
								goto ERROR_EXIT;
							}
							memset(rsa_image_buf, 0, rsa_image_len);
							memcpy(rsa_image_buf, image_buffer, rsa_image_len);
							padding_image_length -= flash_sector_size;
						}
						else
						{
							// check image validation
							ret = image_valid_check(pimage_table, (UINT32)image_buffer);
				            if(ret !=  NoError)
				            {
				                Duster_module_Printf("image invalid");
								set_upgrade_fail_cause(UPGRADE_ERR_INVALID_IMAGE);
								ret = -1;
								goto ERROR_EXIT;
				            }
						}

						//continue store
						if(store_data_into_buf(&ctx, (UINT8 *)image_buffer, image_length) != 0)
						{
							// just return
							set_upgrade_fail_cause(UPGRADE_ERR_OTHER);
							ret = -1;
	             		    goto ERROR_EXIT;
						}
	                    #if 0
	                    if(!fbf_writeflash(pimage_table, image_buffer))
	                    {
	                        CPUartLogPrintf("Write image successfully");
	                    }
						#endif

	                    CPUartLogPrintf("read next image params");
	                    pimage_table = pimage_table->next;
	                }

					if(image_buffer)
					{
						free(image_buffer);
						image_buffer = NULL;
					}
					set_upgrade_status(2);// upgrading
					// trigger server to read the tail of boundry string
	                toservermessage.msgid = 0x99;
	                toservermessage.image_length = 0;
	                osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
					notify_server_download_firmware_done();

					if(PlatformIsTPMifi())
					{
						if(has_rsa)
						{
							Duster_module_Printf("%s TP hash buffer 0x%x len 0x%x",__FUNCTION__, ctx.buffer_start, ctx.bytes_receive - flash_sector_size);
							if(firmware_verify(rsa_image_buf, rsa_image_len, &ctx) != 0)
							{
								CPUartLogPrintf("[SD Upgrade][Fatal Error] signature check failed %x",ret);
								set_upgrade_fail_cause(UPGRADE_ERR_INVALID_IMAGE);
								ret = -4;
		             		    goto ERROR_EXIT;
							}
						}
					}
					else
					{
						// check RSA
						if(has_rsa)
						{
							Duster_module_Printf("%s hash buffer 0x%x len 0x%x",__FUNCTION__, ctx.buffer_start, ctx.bytes_receive - flash_sector_size);
							fbf_hash_start(&hash_ctx);
							if(ctx.bytes_receive > (ctx.buffer_len + flash_sector_size))
							{
								fbf_hash_update(&hash_ctx, ctx.buffer_start, ctx.buffer_len);
								fbf_hash_update(&hash_ctx, ctx.buffer2_start, (ctx.bytes_receive - ctx.buffer_len - flash_sector_size));
							}
							else
								fbf_hash_update(&hash_ctx, ctx.buffer_start, ctx.bytes_receive - flash_sector_size);
							fbf_hash_out(&hash_ctx, hash_out);
							if((ret = rsa_verify((unsigned char *)rsa_image_buf, rsa_image_len, (char *)hash_out, 20)) != 0)
							{
								CPUartLogPrintf("[SD Upgrade][Fatal Error] signature check failed %x",ret);
								set_upgrade_fail_cause(UPGRADE_ERR_INVALID_IMAGE);
								ret = -4;
		             		    goto ERROR_EXIT;
							}
							CPUartLogPrintf("RSA checked done");
						}
					}
					if(check_battery_electic_enough())
					{
						CPUartLogPrintf("[ERROR] battery electric is not enough");
						set_upgrade_status(3);
						set_upgrade_fail_cause(UPGRADE_ERR_LOW_BETTERY);
						ret = -4;
						goto ERROR_EXIT;
					}
					// upgrade FBF file, write into flash
					{
						pre_flash_firmware();

						/*
						// upgrade fbf file
						if(set_upgrade_flag() != NoError)
						{
							CPUartLogPrintf("[Fatal Error]set flag failed");
							set_upgrade_status(3);
							set_upgrade_fail_cause(UPGRADE_ERR_IO);
							ret = -5;
							goto ERROR_EXIT;
						}
						*/
						if(upgrade_ram_fbf_file(&ctx) != NoError)
						{
							set_upgrade_status(3);
							CPUartLogPrintf("[Fatal Error]upgrade FBF failed");
							set_upgrade_fail_cause(UPGRADE_ERR_INVALID_IMAGE);
							ret = -5;
	              		 	goto ERROR_EXIT;
						}
						else
						{
							set_upgrade_status(1);
							CPUartLogPrintf("upgrade FBF success");
							/*
							if(clear_upgrade_flag() != NoError)
							{
								CPUartLogPrintf("[Fatal Error]clear flag failed");
								set_upgrade_status(3);
								set_upgrade_fail_cause(UPGRADE_ERR_IO);
								ret = -5;
								goto ERROR_EXIT;
							}
							*/
						}
					}

	            }
	            else
	            {
	            	set_upgrade_fail_cause(UPGRADE_ERR_INVALID_IMAGE);
	            	ret = -3;
	                goto ERROR_EXIT;
	            }
	        }
			else
			{
				CPUartLogPrintf("%s:get FBF head failed",__FUNCTION__);
				set_upgrade_fail_cause(UPGRADE_ERR_OTHER);
				ret = -2;
	            goto ERROR_EXIT;
			}

	    }
	}

ERROR_EXIT:
	if(image_buffer)
	{
	    free(image_buffer);
		image_buffer = NULL;
	}
	if(rsa_image_buf)
	{
		free(rsa_image_buf);
		rsa_image_buf = NULL;
	}
	if(ret == 0)
	{
		notify_obm_webui_upgrade_flag(&image_table);
		set_upgrade_status(1);
		set_upgrade_fail_cause(UPGRADE_NO_ERROR);
	}
	else if(ret == -1)
	{
		free_fbf_image_list(&image_table);
		set_upgrade_status(3);
		// trigger server to read the tail of boundry string
	    toservermessage.msgid = 0xaa;
	    toservermessage.image_length = 0;
	    osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);

		notify_server_download_firmware_done();
	}
	else if(ret == -2)
	{
		free_fbf_image_list(&image_table);
		// Donot set upgrade fail status, maybe it need re-upload the OTA package by client
		//set_upgrade_status(3);
		MrvCgiEnd();
	}
	else if(ret == -3)
	{
		set_upgrade_status(3);
		MrvCgiEnd();
		// trigger server to do not download file anymore, just close the socket
	    toservermessage.msgid = 0xaa;
	    toservermessage.image_length = 0;
	    osa_status = OSAMsgQSend(CgiToServerMsgQ1, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
	}
	else if(ret == -4)
	{
		// notify upgrade failed
		set_upgrade_status(3);
	}
#endif
	return ret;
}

void notify_server_download_firmware_done(void)
{
	struct http_cgi_msg toservermessage;

	MrvCgiEnd();

	toservermessage.msgid = 0x66;
    toservermessage.length = 0;
    toservermessage.data = NULL;
    OSAMsgQSend(CgiToServerMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_NO_SUSPEND);
}

void UIRefresh_SWUpdate_Change(void)
{
	return;
}



/*
NU_TASK  *TCC_Current_Task_Pointer(VOID)
{


    //Determine if a task thread is executing.
    if ((TCD_Current_Thread) &&
        (((TC_TCB *) TCD_Current_Thread) -> tc_id == TC_TASK_ID))

        //Task thread is running, return the pointer.
        return((NU_TASK *) TCD_Current_Thread);
    else

        //No, task thread is not running, return a NU_NULL.
        return(NU_NULL);
}
*/

extern UINT32 restore_backup_firmware(s_system_info_ctx *ctx);

int  process_server_msg(OSSemaRef SendMsgQ, OSSemaRef ReceiveMsgQ)
{
    char filename[100] = {0};
    int status_return = 0;
	OSA_STATUS osa_status;
	char *xml_get = "www\\temp\\xml_get.tmp", *xml_set = "www\\temp\\xml_set.tmp", *xml_res = "www\\temp\\xml_res.tmp";
	struct mgstat file_stat;
	FILE_ID fid3 = 0;
	PlatformFsType FsType;
	int headerlength = 0, xml_size = 0, i = 0;
	char *buffer_header, *buffer;
	struct http_cgi_msg toservermessage;
     struct http_cgi_msg *ptoservermessage, *pfromservermessage;
	 char fromservermsg_buf[SERVER_TO_CGI_MESSAGE_Q_SIZE];
	 char *content_length;
     char *content_type;
	 char *current_mac = NULL;
     FlashLayoutConfInfo *pFlashLayout = GetFlashLayoutConfig();
	 s_system_info_ctx ctx = {0};
	 int ret = 0 ;

      if(MrvCgiParam(ARG_FILE) != NULL)
      {
           _snprintf(filename, 80, "www\\xmldata\\%s.xml", MrvCgiParam(ARG_FILE));
           cgi_log("MrvCgiParam(ARG_FILE) is: %s", MrvCgiParam(ARG_FILE));
      }

	  if(MrvCgiParam(ARG_METHOD))
      {
       	 if(! strcmp(MrvCgiParam(ARG_METHOD), "get"))
         {
			cgi_log("=== LOG1: CGI GET请求开始 ===");
			cgi_critic("=== LOG1: 请求文件名: %s ===", filename);
			status_return = modify_xml_get_file_nucleus(filename, xml_get);//copy filename to xml_get,repalce the /www/data/*.xml using default data
			cgi_log("=== LOG2: modify_xml_get_file_nucleus返回: %d ===", status_return);
            if(status_return < 0)
            {
            	  cgi_log("=== LOG2: 文件不存在，跳转到file_not_exist ===");
            	  Rdisk_remove(xml_get);
                  goto file_not_exist;
            }
           	status_return = -1;
			/* execute get/set operation with the asked for module */
			if (MrvCgiParam(ARG_MODULE) &&  !strcasecmp(MrvCgiParam(ARG_MODULE), "duster"))
			{
				cgi_log("=== LOG3: 调用duster解析开始 ===");
			    rti_check_run_ticks();
				status_return = cgi_duster_parse("get", xml_get, xml_res);
				cgi_log("=== LOG3: duster解析返回: %d ===", status_return);
				rti_check_run_ticks();
			}
			else
			{
				cgi_critic("CGI: no duster request should not be in here");
				Rdisk_remove(xml_get);
				goto duster_parse_fail;
			}
		    if(!get_need_cgi_response_flag())
		    {
		    	Rdisk_remove(xml_get);
		    	CPUartLogPrintf("%s[get], no need cgi response",__FUNCTION__);
		        goto cgi_cleanup;
		    }
		     if(status_return < 0)
		     {
		         cgi_log("FDI_remove xml_get file");
		         Rdisk_remove(xml_get);
               	 CPUartLogPrintf("cgi_duster_parse failed");
		        goto duster_parse_fail;
		     }
                  cgi_critic("CGI:return from duster parse(get),%d",status_return);

            if(status_return == 0)
            {
    		   	mg_stat(xml_res, &file_stat);
    			fid3 = Rdisk_fopen(xml_res);
    			if(fid3 <= 0)
    			{
    			    cgi_log("Rdisk_fopen xml_res failed");
                    CPUartLogPrintf("open xml file failed");
                   	Rdisk_remove(xml_get);
    			    goto duster_parse_fail;
    			}

                buffer_header = duster_malloc(XML_HTML_HEAD_SIZE + 1);
                if(!buffer_header)
                {
                    CPUartLogPrintf("%s ERR:free memory failed",__FUNCTION__);
                    Rdisk_remove(xml_get);
                    goto duster_parse_fail;
                }
	    		/* init the response headers */
	    		if (!strcasecmp(MrvCgiParam(ARG_FILE), "config_save"))
	    			headerlength = MrvCgiInitXMLHeadersNucleus(file_stat.size, "attachment; filename=config_save.xml",buffer_header, XML_HTML_HEAD_SIZE);
	    		else
	    			headerlength = MrvCgiInitXMLHeadersNucleus(file_stat.size, NULL, buffer_header, XML_HTML_HEAD_SIZE);

                if(headerlength >= XML_HTML_HEAD_SIZE)
                    assert(0);
              /* Write the header of the response */
                toservermessage.msgid = 0x55;
                toservermessage.length = headerlength;
                toservermessage.data = buffer_header;
                buffer_header[headerlength] = '\0';
                cgi_critic("CGI:(GET) the length of the headers is :%d ,the headers of the reponse is :%s", headerlength, buffer_header);
		        osa_status = OSAMsgQSend(SendMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
                assert(osa_status == OS_SUCCESS);
                cgi_critic("CGI:(GET) CGI send the msg to server(the header of the reponse to server) done!");
              //  CPUartLogPrintf("GET: CGI send the msg to server(the header of the reponse to server)");
                cgi_critic("CGI: The result file size is: %d", (unsigned int)file_stat.size);
                if(file_stat.size >= XML_GET_BUF_SIZE)
                      assert(0);
                 buffer = duster_malloc(file_stat.size + 2);
                 if(!buffer)
                 {
                     CPUartLogPrintf("%s ERR:malloc memory failed",__FUNCTION__);
                     assert(0);
                 }
			   /* Write the body of the response */
		         if((xml_size = Rdisk_fread(buffer, sizeof(char), file_stat.size, fid3)) > 0 )
                 {
        			if(xml_size > file_stat.size)
					 {
			    		  CPUartLogPrintf("%s ERR:read xml_res file overflow",__FUNCTION__);
			 		 }
					else
					{
        				 buffer[xml_size] = '\0';
        				 toservermessage.msgid = 0xaa;
                         toservermessage.length = xml_size;
                       	 toservermessage.data = buffer;
                       	 cgi_log("GET: the length of the body is %d, the body of the reponse is :%x",xml_size, buffer);
                        	//CPUartLogPrintf("GET: the length of the body is %d, the body of the reponse is :%x",size, buffer);
#if defined(CGI_XML_CONTENT_DEBUG)
                      	 for(i=0; i<xml_size; i++)
                        	{
                            	CPUartLogPrintf("%c", buffer[i] & 0xff );
                        	}
#endif
        			/** send response body to server using msg queue*/
        			osa_status = OSAMsgQSend(SendMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
                            assert(osa_status == OS_SUCCESS);
                        	cgi_critic("GET: CGI send the msg to server(the body of the reponse to server) done!");
                        	//CPUartLogPrintf("GET: CGI send the msg to server(the body of the reponse to server)");
                    }
                }
                else
                {
                     CPUartLogPrintf("%s ERR:read xml_res file failed",__FUNCTION__);
					 Rdisk_remove(xml_res);
		 			 Rdisk_remove(xml_get);
					 goto duster_parse_fail;
                }
		  		Rdisk_remove(xml_res);
		 		Rdisk_remove(xml_get);
             }
             if(fid3 >0)
             {
		  		 Rdisk_fclose(fid3);
				 fid3 = 0;
             }
	}
      else if(! strcmp(MrvCgiParam(ARG_METHOD), "set"))
      {
		FILE_ID save_fid;

		/* Open a temporary file in which the body of
			 * POST request will be stored */
		save_fid = Rdisk_fopen(xml_set);
    	if (save_fid <= 0)
		{
			goto cgi_cleanup;
		}

            	memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);
		/* Read the body of the POST request using msg_queue */
		osa_status = OSAMsgQRecv(ReceiveMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
		assert(osa_status == OS_SUCCESS);
            	cgi_log("Reced POST body data msg");
           	//CPUartLogPrintf("Reced POST body data msg from server");
		pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;
		cgi_critic("CGI:Reced POST body data msgid:%d, length:%d, data buffer:%x",pfromservermessage->msgid, pfromservermessage->length, pfromservermessage->data);
		if( pfromservermessage->msgid == 0x55 )
		{
			if( pfromservermessage->length > 0 )
			{
			      cgi_log("Reced POST body data msg and write the data to xml file");
			      buffer = duster_malloc(pfromservermessage->length + 1);
			      if(buffer)
			      {
    			           memcpy(buffer, pfromservermessage->data, pfromservermessage->length);
    			            buffer[pfromservermessage->length] = '\0';
    			            Rdisk_fwrite(buffer, sizeof(char), pfromservermessage->length, save_fid);
    			            //cgi_log("xml_set file content is :%s", buffer);
    			            duster_free(buffer);
			       }
			   	  Rdisk_fclose(save_fid);
				  save_fid = 0;
			 }
			 else
			 {// wrong xml file, just continue
			     cgi_critic("CGI:[POST][ERROR]can not received XML file from server");
			     goto miss_xml_fail;
			 }
		}
		/* Execute get/set according to the asked for module */
    	if (MrvCgiParam(ARG_MODULE) &&  !strcasecmp(MrvCgiParam(ARG_MODULE), "duster"))
        {
            rti_check_run_ticks();
			if(restore_config == 1)
				status_return = cgi_duster_parse("restore", xml_set, xml_res);
			else
				status_return = cgi_duster_parse("set", xml_set, xml_res);//xml_res is not used in set operation

            rti_check_run_ticks();
			if(!get_need_cgi_response_flag())
		    {
		    	Rdisk_remove(xml_set);
				Rdisk_remove(xml_res);
		    	CPUartLogPrintf("%s[set], no need cgi response",__FUNCTION__);
		        goto cgi_cleanup;
		    }
	   		 cgi_critic("CGI:Return from cgi_duster_parse(set):%d", status_return);
			 if(status_return < 0 )
			 {
				 Rdisk_remove(xml_set);
				 Rdisk_remove(xml_res);
				 CPUartLogPrintf("cgi_duster_parse failed");
				 goto duster_parse_fail;
			 }
			//Rdisk_remove(xml_set);
			cgi_critic("CGI: XML filename is: %s", filename);
			//status_return = modify_xml_get_file_nucleus(filename, xml_get);//copy filename to xml_get,repalce the /www/data/*.xml using default data
            if(status_return < 0)
            {
            	  CPUartLogPrintf("copy file failed");
				  Rdisk_remove(xml_set);
				  Rdisk_remove(xml_res);
                  goto file_not_exist;
            }

			status_return = cgi_duster_parse("get", filename, xml_res);//get the xml date to xml_res
			//CPUartLogPrintf("Return from cgi_duster_parse(set-get):%d", status_return);
			if(status_return < 0 )
			{
			    Rdisk_remove(xml_set);
				//Rdisk_remove(xml_get);
				Rdisk_remove(xml_res);
                CPUartLogPrintf("cgi_duster_parse failed");
                goto duster_parse_fail;
			}
		}
		else
		{
			Rdisk_remove(xml_set);
			//Rdisk_remove(xml_get);
			Rdisk_remove(xml_res);
            CPUartLogPrintf("duster request not support");
            goto duster_parse_fail;
    	}
		/* init the repsonse header and body */
		mg_stat(xml_res, &file_stat);
		fid3 = Rdisk_fopen(xml_res);
    	if (fid3 <= 0) {
			 CPUartLogPrintf("%s ERR: open xml_res file failed",__FUNCTION__);
			 Rdisk_remove(xml_set);
			// Rdisk_remove(xml_get);
			 Rdisk_remove(xml_res);
			 goto duster_parse_fail;
		}

    	buffer_header = duster_malloc(XML_HTML_HEAD_SIZE + 1);
         if(buffer_header == NULL)
         {
             CPUartLogPrintf("%s ERR:free memory failed",__FUNCTION__);
		     assert(0);
         }
 		headerlength = MrvCgiInitXMLHeadersNucleus(file_stat.size, NULL, buffer_header, XML_HTML_HEAD_SIZE);
	    toservermessage.msgid = 0x55;
        toservermessage.length = headerlength;
        toservermessage.data = buffer_header;

        buffer_header[headerlength] = '\0';
        cgi_critic("CGI:(SET) the headers of the reponse is :%s", buffer_header);

	   osa_status = OSAMsgQSend(SendMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
       assert(osa_status == OS_SUCCESS);
       cgi_critic("CGI:(SET)CGI send the msg to server(the header of the reponse to server)");
       cgi_critic("CGI: The result file size is: %d", (unsigned int)file_stat.size);
        if(file_stat.size >= XML_GET_BUF_SIZE)
             assert(0);
        buffer = duster_malloc(file_stat.size + 2);
        if(buffer)
 		{
 			if((xml_size = Rdisk_fread(buffer, sizeof(char), file_stat.size, fid3)) > 0 )
 			{
 			    if(xml_size > file_stat.size)
 			    {
 			         CPUartLogPrintf("%s ERR:read xml_res file overflow",__FUNCTION__);
 			    }
 			    else
 			    {
 				     buffer[xml_size] = '\0';
 				     toservermessage.msgid = 0xaa;
 			         toservermessage.length = xml_size;
 			       	 toservermessage.data = buffer;

 			       	 cgi_log("SET:the body of the reponse is :%x, length is:%d", buffer, xml_size);
 				    /** send get reponse body to server using msg queue*/
 				     osa_status = OSAMsgQSend(SendMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
 			         assert(osa_status == OS_SUCCESS);

 			         cgi_log("SET:CGI send the msg to server(the body of the reponse to server)");
 			        	//CPUartLogPrintf("SET:CGI send the msg to server(the body of the reponse to server)");
 			      }
 			}
 			else
 			{
 			     CPUartLogPrintf("%s:read xml_res file failed",__FUNCTION__);
				 Rdisk_remove(xml_set);
				// Rdisk_remove(xml_get);
				 Rdisk_remove(xml_res);
				 goto duster_parse_fail;
 			}
 		}
		else
		{
			CPUartLogPrintf("%s ERR:free memory failed",__FUNCTION__);
			 assert(0);
		}
		if(fid3 > 0)
		{
			Rdisk_fclose(fid3);
			fid3 = 0;
		}
		Rdisk_remove(xml_res);
	    Rdisk_remove(xml_set);
		//Rdisk_remove(xml_get);
	  }
	  else {
	  		cgi_err("%s: unsupport method %s",__FUNCTION__, MrvCgiParam(ARG_METHOD));
			OSAMsgQRecv(ReceiveMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OS_NO_SUSPEND);
			pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;
			cgi_critic("%s:will discard data, msgid:%d, length:%d, data buffer:%x",pfromservermessage->msgid, pfromservermessage->length, pfromservermessage->data);
			goto miss_xml_fail;
	  }
	}
	if (MrvCgiParam("Action")) {
             CPUartLogPrintf("Action item is exist in query_string");
            if(!strcmp(MrvCgiParam("Action"),"Upload"))
            {
                 CPUartLogPrintf("Action is Upload");
                if ((content_length = MrvGetEnv(&g_cgi_param, "CONTENT_LENGTH")) == NULL)
                   CPUartLogPrintf("Upgrade: No body data transfered");
                if((content_type = MrvGetEnv(&g_cgi_param,"CONTENT_TYPE")) == NULL)
                   CPUartLogPrintf("Upgrade: No CONTENT_TYPE");

                CPUartLogPrintf("Action file is: %s",MrvCgiParam(ARG_FILE));
                if (!strcmp(MrvCgiParam(ARG_FILE),"upgrade")){
                     //upgrade_lock();
                     memset(g_current_upgrade_mac, 0, sizeof(g_current_upgrade_mac));
                     strncpy(g_current_upgrade_mac, g_current_mac, sizeof(g_current_upgrade_mac) - 1);

        			pre_handle_upgrade();
        			set_system_in_upgrade_status();
        			lwip_set_ul_non_cpy(0);
                     if(!PlatformIsMinSystem())
                     {
                   	 	//SetCFUN0();
						set_cfun0_for_upgrade();
						OSATaskSleep(100);
                     }
				   	  // When set CFUN0 done, using ps buf for temp store fbf file.
				  	 clear_ps_buf();

                     CPUartLogPrintf("Upgrade: start!");
					 // API for ZiMi
					 swupgrade_begin_led_on();
					 LED_SWUpgrade_On();
				   	 UIRefresh_SWUpdate_Change();
					 ret = upgrade_and_flash(content_length, content_type);
					 CPUartLogPrintf("Upgrade: upgrade_and_flash return %d", ret);
                     if(ret != 0)
                     {
                        if(ret >= ERR_NEED_OBM_UPGRAGE_OK)
                        {
                        	CPUartLogPrintf("Upgrade: set upgrade status, may need OBM to do real upgrade!!");
                        	// maybe need OBM to do really upgrade
                        	if(ret == ERR_NEED_OBM_UPGRAGE_OK)
                        	{
                        		// 1: means FBF download, check and validation without any error
                        		set_upgrade_status(1);
								set_upgrade_fail_cause(UPGRADE_NO_ERROR);// system memory issue
                        		swupgrade_done_led_on(1);
                        	}
							else
							{
								// 3: means FBF download, check and validation with some errors
								set_upgrade_status(3);
								if(ret == ERR_NEED_OBM_UPGRAGE_MEM)
								{
									set_upgrade_fail_cause(UPGRADE_ERR_MEMORY);// system memory issue
								}
								else if(ret == ERR_NEED_OBM_UPGRAGE_FILE_INVALID)
								{
									set_upgrade_fail_cause(UPGRADE_ERR_INVALID_IMAGE);// invalid upgrade file
								}
								else if(ret == ERR_NEED_OBM_UPGRAGE_FLASHIO)
								{
									set_upgrade_fail_cause(UPGRADE_ERR_IO);// burn operation failed
								}
								else if(ret == ERR_NEED_OBM_UPGRAGE_HTTP)
								{
									set_upgrade_fail_cause(UPGRADE_ERR_UPLOAD);//error when upload the upgrade error
								}
								else if(ret == ERR_NEED_OBM_UPGRAGE_FILE_SIZE)
								{
									set_upgrade_fail_cause(UPGRADE_ERR_IMAGE_SIZE);//UPGRADE_ERR_IMAGE_SIZE
								}
								else
								{
									set_upgrade_fail_cause(255);// unknown error
								}
                        		swupgrade_done_led_on(0);
							}

							goto cgi_cleanup;
                        }
                     	// upgrade failed, just reset
                     	CPUartLogPrintf("Upgrade: upgrade_and_flash failed %d", ret);
						if(ret == -5)
							set_mini_system_ram_flag();
						swupgrade_done_led_on(0);
						//CPUartLogPrintf("Upgrade: wait for 3s");
						//OSATaskSleep(3*200);
						//reset directly
						//CPUartLogPrintf("Upgrade: reset");
                     	//watchdog_reset();
                     }
					 else
					 {
					 	CPUartLogPrintf("Upgrade: upgrade and install done!");

						 UIRefresh_SWUpdate_Change();

	                    //upgrade_unlock();
	                    CPUartLogPrintf("Upgrade: erase file system...");
						/*rjin, 20140415, call common API to erase filesystem */

                        FsType = PlatformGetFsType();

                        switch(FsType)
                        {
#if defined(LFS_FILE_SYS)
                            case LFS_FS_TYPE:
                            {
                                LfsMutexLockALL();
                                break;
                            }
#endif

#if defined(FAT16_FILE_SYS)
                            case FAT_FS_TYPE:
                            {
                                FatCacheLock(TRUE);
                                break;
                            }
#endif
                            default:
                            {
                                ASSERT_EXT( FALSE, "Fs Type %d", FsType ) ;
                                break;
                            }
                        }

						Flash_EraseFATPartition();
						SetFactoryRstFlag(TRUE);
						CPUartLogPrintf("Upgrade: erase file system done!");
						if(!PlatformIsMinSystem())
						{
							#ifdef ERASE_PSM_DATA
							set_obm_upgrade_success_flag();
							backup_psm_item();
							CPUartLogPrintf("Upgrade: erase psm...");
							erase_psm_block(1);
							CPUartLogPrintf("Upgrade: erase psm done!");
							#endif
						}
						else
						{
							// force to erase psm in mini system upgrade
							CPUartLogPrintf("Upgrade: erase psm...");
							erase_psm_block(1);
							CPUartLogPrintf("Upgrade: erase psm done!");
						}
						set_full_system_ram_flag();
						set_system_in_upgrade_flag();

						LED_SWUpgrade_Off();

						// API for ZiMi
						swupgrade_done_led_on(1);
						//OSATaskSleep(2*200);
						// watchdog_reset();// reset router
						//watchdog_reset();
					}
                }
		}
		else if(!strcmp(MrvCgiParam("Action"),"RestoreFw"))
		{
			 MrvCgiEnd();
			 // restore firmware to flash
			 memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);
			 osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
			 assert(osa_status == OS_SUCCESS);
		     pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;
			 if( pfromservermessage->msgid == 0x56 )
			 {
			 	if( pfromservermessage->length > 0 )
			 	{
			 		ctx.start = pfromservermessage->data;
					ctx.len = pfromservermessage->length;
			 		if((status_return = restore_backup_firmware(&ctx)) != 0)
					{
						CPUartLogPrintf("restore firmware failed");
					}
			 	}
			 }
			 else if(pfromservermessage->msgid == 0xaa)
			 {
			 	CPUartLogPrintf("get upload restore firmware failed");
			 }
		}
		else if(!strcmp(MrvCgiParam("Action"),"BackupFwStart"))
		{

			 MrvCgiEnd();
			 // backup firmware from flash
			 memset(fromservermsg_buf, 0, SERVER_TO_CGI_MESSAGE_Q_SIZE);
			 osa_status = OSAMsgQRecv(ServerToCgiMsgQ, (UINT8 *)fromservermsg_buf, SERVER_TO_CGI_MESSAGE_Q_SIZE, OSA_SUSPEND);
			 assert(osa_status == OS_SUCCESS);
		     pfromservermessage = (struct http_cgi_msg *)fromservermsg_buf;
			 if( pfromservermessage->msgid == 0x57 )
			 {
		 		if((status_return = backup_firmware()) != 0)
				{
					CPUartLogPrintf("backup firmware failed");
					//set_backup_firmware_status(3);
				}
				else
				{
					// succes
					CPUartLogPrintf("backup firmware successfully");
					//set_backup_firmware_status(1);
				}
			 }
		}
     }

cgi_cleanup:
	 MrvCgiEnd();
	 if(fid3 > 0)
	 {
	 	Rdisk_fclose(fid3);
		fid3 = 0;
	 }
	 return 0;

duster_parse_fail:
	MrvCgiEnd();
	if(fid3 > 0)
	 {
	 	Rdisk_fclose(fid3);
		fid3 = 0;
	 }
	toservermessage.msgid = 0x99;
    toservermessage.length = 0;
    toservermessage.data = NULL;
    osa_status = OSAMsgQSend(SendMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
	return 0;
file_not_exist:
     MrvCgiEnd();
	 if(fid3 > 0)
	 {
	 	Rdisk_fclose(fid3);
		fid3 = 0;
	 }
	 /** send error to server using msg queue*/
	toservermessage.msgid = 0x88;
    toservermessage.length = 0;
    toservermessage.data = NULL;
    osa_status = OSAMsgQSend(SendMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
    assert(osa_status == OS_SUCCESS);
	return 0;
miss_xml_fail:
    MrvCgiEnd();
	if(fid3 > 0)
	 {
	 	Rdisk_fclose(fid3);
		fid3 = 0;
	 }
	 /** send error to server using msg queue*/
	toservermessage.msgid = 0x89;
    toservermessage.length = 0;
    toservermessage.data = NULL;
    osa_status = OSAMsgQSend(SendMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
    assert(osa_status == OS_SUCCESS);
	return 0;
}
void RegisterToCgi(OSMsgQRef ToCgiMSGQ, OSMsgQRef ToServerMSGQ)
{
	CgiToServerMsgQ_APP = ToServerMSGQ;
	ServerToCgiMsgQ_APP = ToCgiMSGQ;
}
void CGI_task(void *argc)
{
      char *buffer = NULL;
      char *buffer_header = NULL;
      char envmessage_buf[SERVER_TO_CGI_ENV_MESSAGE_Q_SIZE];

      struct http_cgi_msg toservermessage;
      struct http_cgi_msg *pServerEnvMessage;
      OSA_STATUS osa_status;
      char server_type = 0;
      while(1)
      {
         cgi_log("Enter CGI task");
         //OSATaskSleep(10);
 #if 0
         OSASemaphoreAcquire(_cgiparamreadyRef, OSA_SUSPEND);
         OSASemaphoreAcquire(_cgiparamRef, OSA_SUSPEND);
         gstart = MrvCgiProcessFormNucleus(&g_cgi_param);
         OSASemaphoreRelease(_cgiparamRef);
#endif
	  osa_status = OSAMsgQRecv(ServerToCgiEnvMsgQ, (UINT8 *)envmessage_buf, SERVER_TO_CGI_ENV_MESSAGE_Q_SIZE, OSA_SUSPEND);
		assert(osa_status == OS_SUCCESS);
	  pServerEnvMessage = (struct http_cgi_msg *)envmessage_buf;
	  server_type = pServerEnvMessage->msgid;
	  if(pServerEnvMessage->data)
	  {
	  	 OSASemaphoreAcquire(_cgiparamRef,OSA_SUSPEND);
	  	 gstart = MrvCgiProcessFormNucleus((struct cgi_env_block *)pServerEnvMessage->data);
		 OSASemaphoreRelease(_cgiparamRef);
	  }
	  else
	  	continue;

       // OSATaskSleep(1);
       cgi_log("CGI:Get trigger msg, type %x",server_type);

	if(server_type == 0x11)//HTTP server
	{
	     if(process_server_msg(CgiToServerMsgQ, ServerToCgiMsgQ) < 0)
	     	{
			continue;
			#if 0
			     toservermessage.msgid = 0x77;
		            toservermessage.length = 0;
		           toservermessage.data = NULL;
		           osa_status = OSAMsgQSend(CgiToServerMsgQ, sizeof(toservermessage), (UINT8*)&toservermessage, OSA_SUSPEND);
		           assert(osa_status == OS_SUCCESS);
		          osa_status = OSATaskSuspend(CgiTaskRef);
		          assert(osa_status == OS_SUCCESS);
		          CPUartLogPrintf("cgi task error");
			#endif
	     	}
	}
//	#if defined(HOJY_APP_SERVER)
	 else if(server_type == 0x22)
	 {
	 	if(process_server_msg(CgiToServerMsgQ_APP, ServerToCgiMsgQ_APP)< 0)
			continue;
	 }
//	 #endif
       MrvCgiEnd();
    }
}


void MrvInitCgi(void)
{
    OSA_STATUS osa_status;
   #if 1
   MrvCgiInit();
   #endif

   /*msg queue for receiving message of env buffer information*/
     osa_status = OSAMsgQCreate(&ServerToCgiEnvMsgQ,
                                  "ServerToCgiEnvMsgQ",
                                  SERVER_TO_CGI_ENV_MESSAGE_Q_SIZE,
                                  SERVER_TO_CGI_ENV_MESSAGE_Q_MAX,
                                  OS_FIFO);
    assert(osa_status == OS_SUCCESS);

   if(CgiTaskStack == NULL)
   {
            cgi_log("Out of memory in cgi task stack!\n");
            return;
   }
   #if 1

   if (OSATaskCreate(&CgiTaskRef,
                 (void *)CgiTaskStack,
                 CGI_STACK_SIZE,
                 CGI_TASK_PRIORITY,
                 (char*)"cgiTask",
                 CGI_task,
                 0) != 0)
                 {
                    cgi_log("Create cgi task failed!\n");
                    return;
                 }
     #endif
}

