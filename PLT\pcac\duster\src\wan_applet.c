#include "string.h"
#include <duster_applets.h>
#include "psm_wrapper.h"
#include "wan_applet.h"
#include "device_management_applet.h"
#include "Dialer_Task.h"
//#include "uapapi.h"
#include "ci_dev.h"
#include "ci_sim.h"
#include "ci_mm.h"
#include "mm_api.h"
#include "ps_api.h"
#include "sim_api.h"

#ifdef QUECTEL_PROJECT_CUST
#include "cgpio.h"
#endif

#include "connect_management.h"
#include "MrvXML.h"
#include "platform.h"

/*added for additional APN List by shoujunl @131216 start*/
#include "MRD.h"
#include "MRD_structure.h"
#include "FlashPartition.h"
#if !defined(CRANE_WEBUI_SUPPORT)
#include "flash_api.h"
#endif
#include "ReliableData.h"
/*added for additional APN List by shoujunl @131216 end*/

#include "lwip_api.h"

#if defined(CRANE_WEBUI_SUPPORT)
#if 0
#undef Duster_module_Printf
#define Duster_module_Printf(level, fmt,args...) \
	{CPUartLogPrintf(fmt, ##args);}
#endif
extern char * strdup(const char *str);
extern void getTotalSuppOpeNum(UINT32 atHandle, INT32 *totalNum);
extern void resetTotalSuppOpeNum(UINT32 atHandle);
#ifdef QUECTEL_PROJECT_CUST
extern int CM_Start_Exist_Connection(int conn_num, BOOL force_setup);
#endif
extern void WIFI_SleepTimer_Change(void);
extern void UIRefresh_ClientNum_Change(void);
extern char *get_network_name(int NameType, int channel);
extern UINT32 CurSelectNWMode;
extern UINT32 searchNW_tick;
#endif


//#define OPERNAME_SACCACHE
#define SEARCHNWTASKSTACKSIZE 1024
//min connection number, created by network, sync with CM
#define MIN_CONNUM_CREATBY_NETWORK			0x80
#define DEFAULE_CID_NUM 4
#define TYPE_V4V6 0
#define TYPE_V4 1
#define TYPE_V6 2

#define RULE_SHOW_PLMN 0x01
#define RULE_SHOW_SPN 0x02

#define NETWORK_NAME_TYPE 0
#define ROAMING_NETWORK_NAME_TYPE 1

#define ROAMING_TYPE 1
#define NONROAMING_TYPE 0

#if !defined(CRANE_WEBUI_SUPPORT)
#define FLASH_BLOCK get_flash_block_size()
#endif
#define NoError            					0x0

static UINT32 gSearchNWTaskStack[SEARCHNWTASKSTACKSIZE>>2] = {0xffffffff};
OSTaskRef gSearchNWTask = NULL;

UINT32 disconnectnetwork = 1;
UINT32 g_get_spn_done = 0;
UINT32 firstInstantSwitchTick = 0;
UINT32 HistoryInstantSwitchTick = 0;
#if !defined(CRANE_WEBUI_SUPPORT)
UINT32 searchNW_tick = 0;
UINT32 CurSelectNWMode = 2;   //0: auto, 1: manual, 2: unknown
#endif
int wanAction = 0;

BOOL AllowInstantSwitch = FALSE;
BOOL cellularFlag=TRUE;
BOOL ManualSelectNW = FALSE;
BOOL UpdateVersionFlag = TRUE;
UINT32 QueryInterval=1,QueryTime=0,EngiMode=0;
char NWmode = CI_DEV_NW_TRIP_MODE_LTE;
int PreferLteType = 0xFFFFFFF;
char g_msisdn_webui[16];
int g_quota;


extern GlobalParameter_Duster2Dialer gUI2DialerPara;
extern GlobalParameter_Dialer2Duster gDialer2UIPara;

extern long rx_rate;
extern long tx_rate;
extern UINT32 cur_tick;
extern char IMEI_WEB[];
extern int gTotalSuppOpeNumBak;
extern UINT8 ATCmdSvrRdy;

extern BOOL cops_rsp_flag;
extern BOOL SelectNWFlag;
extern BOOL searchNW_exit_flag;
extern int gCsActDetail ;

#if defined(CRANE_WEBUI_SUPPORT)
extern int gPsActDetail ;
#else
extern int gPSActDetail ;
#endif

extern UINT32 g_set_atcops_done;
extern s_switch_image_ctx switch_ctx;

/*extern charge infomation */
extern enum CHG_state_t CHG_state;
extern enum Bat_connect_t Bat_connect_state;
extern unsigned char  GetBatteryPercent(void);
extern unsigned int GetBatteryVoltage(void);
extern BOOL PSDetachFlag;
extern char IMSI[16];
extern char ICCID[10];
extern char g_MT_pPDP_name[];
extern char g_MT_sPDP_name[];

extern UINT32 getCellID(UINT32 atHandle);
extern void SearchNetworkTask(void);
extern LinkStatus_Context * CM_Get_LinkStatus(char connection_num);
extern char * duster_strdup(const char *str);
extern void free_link_status(LinkStatus_Context *link_status);
#ifndef NO_LWIP_NETIF
extern void netif_get_default_nw_ip6addr(lte_ipv6_addr *ip6_ptr);
#endif
extern BOOL psNvmCheckUserModeConfigInfo(UINT32 nw_mode, UINT32 prefer_mode,	UINT32 band_priority_flag);
extern int dialer_Check_NW(UINT32 AT_channel);
extern void clearErrorIndFlagAll(void);
#if defined(CRANE_WEBUI_SUPPORT)
extern INT32 get_current_reg_status(UINT32 atHandle);
#else
extern int get_current_reg_status(void);
#endif
extern void CM_NWSwitch_3Gonly(void);
#ifdef QUECTEL_PROJECT_CUST
extern int check_SIM_status_simple(void);
#endif

extern unsigned char IS_LTE_W_PS;

extern Mnc_Apn *IMSI_APN;
extern CiSimStatus gSimStatus;
extern int gCurrentPSRegStatus;
extern char CurrentRoamingStatus;
extern int engi_RSSI;
extern int engi_RSRP;
extern int engi_SINR;
extern int engi_CellID;
extern UINT8 gMncLen;



extern void enable_spi_nor_4byte_mode(void);
extern void disable_spi_nor_4byte_mode(void);
extern void CM_clear_first_dial_flag(void);
extern BOOL	stat_reach_limit(void);
extern char *itoa(int value,char *string,int radix);
extern void wan_cellular_stop_pdp(int conn_num);
#if !defined(CRANE_WEBUI_SUPPORT)
extern int strcasecmp(const char *s1, char *s2);
#endif
extern int strncasecmp(const char *s1, const char *s2, size_t n);
extern void configureLwipNetif(UINT8 status, UINT8 cid, UINT8 rndis_type);

typedef struct
{
	char rulename[64];
	char apn[30];
	char *ipv4;
	char *v4dns1;
	char *v4dns2;
	char *v4gateway;
	char *v4netmask;
	char *ipv6;
	char *g_ipv6;
	char *v6dns1;
	char *v6dns2;
	char *v6gateway;
	char *v6netmask;
	int curconntime;
	int totalconntime;
	char connnum;
	char pconnnum;
	char success;
	char isdefault;
	char secondary;
	char cid;
	char ip_type;
} Duster_UI_PDP_Context;

typedef struct
{
	int dis_cond;
	int spn_exist;
	char spn_name[17];
} s_spn_info;

s_spn_info SPN_INFO;

// API provided by ATC
extern INT32 getSimType(UINT32 reqHandle);

unsigned char is_usim_card(void)
{
	UINT32 reqHandle = 0;
	INT32 simType = 0;

	reqHandle = isMasterSim0() ? IND_REQ_HANDLE : IND_REQ_HANDLE_1;

	simType = getSimType(reqHandle);

	if (simType == AT_SIM_CARD_TYPE_USIM)
		return TRUE;
	else
		return FALSE;
}

/*
0: no service
1: extremely weak
2: weak
3: normal
4: strong
5: extremely strong
*/
int get_signal_level(void)
{
	int level = 0;
	int sysMainMode, sysMode;

	struct mmExtendedSignal extendedSignal;

	getExtendedSignal(1, &extendedSignal);
	getSysMode(1, (INT32 *)&sysMainMode, (INT32 *)&sysMode);

	if (sysMainMode == 3)
	{
		if (extendedSignal.rssi == 0 || extendedSignal.rssi == 99)
		 	level = 0;
		else if (extendedSignal.rssi > 0 && extendedSignal.rssi <= 6)
			level = 1;
		else if (extendedSignal.rssi > 6 && extendedSignal.rssi <= 13)
			level = 2;
		else if (extendedSignal.rssi > 13 && extendedSignal.rssi <= 18)
			level = 3;
		else if (extendedSignal.rssi > 18 && extendedSignal.rssi <= 23)
			level = 4;
		else if (extendedSignal.rssi > 23 && extendedSignal.rssi <= 63)
			level = 5;
		else
			level = 0;
	}
	else if (sysMainMode == 5 || sysMainMode == 15)
	{
		if (extendedSignal.rscp == 255)
		 	level = 0;
		else if (extendedSignal.rscp > 0 && extendedSignal.rscp <= 16)
			level = 1;
		else if (extendedSignal.rscp > 16 && extendedSignal.rscp <= 21)
			level = 2;
		else if (extendedSignal.rscp > 21 && extendedSignal.rscp <= 26)
			level = 3;
		else if (extendedSignal.rscp > 26 && extendedSignal.rscp <= 30)
			level = 4;
		else if (extendedSignal.rscp > 30 && extendedSignal.rscp <= 96)
			level = 5;
		else
			level = 0;
	}
	else if (sysMainMode == 17)
	{
		if (extendedSignal.rsrp== 255)
			level = 0;
		else if (extendedSignal.rsrp > 0 && extendedSignal.rsrp <= 24)
			level = 1;
		else if (extendedSignal.rsrp > 24 && extendedSignal.rsrp <= 33)
			level = 2;
		else if (extendedSignal.rsrp > 33 && extendedSignal.rsrp <= 42)
			level = 3;
		else if (extendedSignal.rsrp > 42 && extendedSignal.rsrp <= 54)
			level = 4;
		else if (extendedSignal.rsrp > 54 && extendedSignal.rsrp <= 97)
			level = 5;
		else
			level = 0;
	}
	else
		level = 0;

	return level;
}

/*get saved signal level before network switch out*/
int get_saved_signal_level(void)
{
	int level = 0;
	struct mmExtendedSignal savedSignal;
	int savedSysMainMode;

	//SIM1
	getSavedSignal(1, &savedSignal);
	getSavedSysMode(1, (INT32 *)&savedSysMainMode);
	duster_Printf("%s: gSavedSysMainMode %d, gSavedExtendedSignalRssi %d, gSavedExtendedSignalRscp %d, gSavedExtendedSignalRsrp %d", __func__,
								   savedSysMainMode, savedSignal.rssi, savedSignal.rscp, savedSignal.rsrp);
	if (savedSysMainMode == 3)
	{
		if (savedSignal.rssi== 0 || savedSignal.rssi == 99)
		 	level = 0;
		else if (savedSignal.rssi > 0 && savedSignal.rssi <= 6)
			level = 1;
		else if (savedSignal.rssi > 6 && savedSignal.rssi <= 13)
			level = 2;
		else if (savedSignal.rssi > 13 && savedSignal.rssi <= 18)
			level = 3;
		else if (savedSignal.rssi > 18 && savedSignal.rssi <= 23)
			level = 4;
		else if (savedSignal.rssi > 23 && savedSignal.rssi <= 63)
			level = 5;
		else
			level = 0;
	}
	else if (savedSysMainMode == 5 || savedSysMainMode == 15)
	{
		if (savedSignal.rscp == 255)
		 	level = 0;
		else if (savedSignal.rscp > 0 && savedSignal.rscp <= 16)
			level = 1;
		else if (savedSignal.rscp > 16 && savedSignal.rscp <= 21)
			level = 2;
		else if (savedSignal.rscp > 21 && savedSignal.rscp <= 26)
			level = 3;
		else if (savedSignal.rscp > 26 && savedSignal.rscp <= 30)
			level = 4;
		else if (savedSignal.rscp > 30 && savedSignal.rscp <= 96)
			level = 5;
		else
			level = 0;
	}
	else if (savedSysMainMode == 17)
	{
		if (savedSignal.rsrp == 255)
			level = 0;
		else if (savedSignal.rsrp > 0 && savedSignal.rsrp <= 24)
			level = 1;
		else if (savedSignal.rsrp > 24 && savedSignal.rsrp <= 33)
			level = 2;
		else if (savedSignal.rsrp > 33 && savedSignal.rsrp <= 42)
			level = 3;
		else if (savedSignal.rsrp > 42 && savedSignal.rsrp <= 54)
			level = 4;
		else if (savedSignal.rsrp > 54 && savedSignal.rsrp <= 97)
			level = 5;
		else
			level = 0;
	}
	else
		level = 0;

	return level;
}

//ICAT EXPORTED FUNCTION - WAN,APPLET,GET_RSSI
UINT32 get_rssi(void)
{
	UINT32 RSSI;
	INT32 sysMainMode, sysMode;
	struct mmExtendedSignal extendSignal;

	getExtendedSignal(1, &extendSignal);
	getSysMode(1, &sysMainMode, &sysMode);

	if(sysMainMode == 3)                                   //GSM
	{
		if (bspGetBoardType() == TIGX_MIFI)
		{
			if (extendSignal.rssi == 0 || extendSignal.rssi == 99)
				RSSI = extendSignal.rssi;
			else
				RSSI =  extendSignal.rssi/2 + 1;

			if (RSSI > 31)
				RSSI = 31;
		}
		else
			RSSI = extendSignal.rssi;      // 0 -- 63
	}
	else if((sysMainMode == 5) || (sysMainMode == 15))    //WCDMA or TD
	{
		if (bspGetBoardType() == TIGX_MIFI)
		{
			if (extendSignal.rscp == 255)
			{
				RSSI = 99;
			}
			else if (extendSignal.rscp > 0 && extendSignal.rscp < 17)
			{
				RSSI = 1; /* 0 -2*/
			}
			else if (extendSignal.rscp >= 17 && extendSignal.rscp < 22)
			{
				RSSI = 4; /* 2 -5*/
			}
			else if (extendSignal.rscp >= 22 && extendSignal.rscp < 27)
			{
				RSSI = 7; /* 5 -8*/
			}
			else if (extendSignal.rscp >= 27 && extendSignal.rscp < 31)
			{
				RSSI = 10; /* 8 -12*/
			}
			else if (extendSignal.rscp >= 31 && extendSignal.rscp <= 96)
			{
				RSSI = 24;
			}
			else
				RSSI = 99;
		}
		else
			RSSI = extendSignal.rscp;      // 0 -- 96

	}
	else if( sysMainMode == 17)                            //LTE
	{
		if (bspGetBoardType() == TIGX_MIFI)
		{
			if (extendSignal.rsrp == 255)
			{
				RSSI = 99;
			}
			else if (extendSignal.rsrp > 0 && extendSignal.rsrp < 25)
			{
				RSSI = 1; /* 0 -2*/
			}
			else if (extendSignal.rsrp >= 25 && extendSignal.rsrp < 34)
			{
				RSSI = 4; /* 2 -5*/
			}
			else if (extendSignal.rsrp >= 34 && extendSignal.rsrp < 43)
			{
				RSSI = 7; /* 5 -8*/
			}
			else if (extendSignal.rsrp >= 43 && extendSignal.rsrp < 55)
			{
				RSSI = 10; /* 8 -12*/
			}
			else if (extendSignal.rsrp >= 55 && extendSignal.rsrp <= 97)
			{
				RSSI = 24;
			}
			else
				RSSI = 99;
		}
		else
			RSSI = extendSignal.rsrp;      // 0 -- 97
	}
	else
	{
		if (bspGetBoardType() == TIGX_MIFI)
		{
			if (extendSignal.rssi == 0 || extendSignal.rssi == 99)
				RSSI = extendSignal.rssi;
			else
				RSSI =  extendSignal.rssi/2 + 1;

			if (RSSI > 31)
				RSSI = 31;
		}
		else
			RSSI = extendSignal.rssi;      // 0 -- 63
	}

	duster_Printf("%s: gSysMainMode is %d, RSSI is %d", __FUNCTION__, sysMainMode, RSSI);

	return RSSI;
}

int wan_info_gather(wan_info_t *p_wan_sys_info)
{
	int     wan_ip_up = 0;
	char *str = NULL;
	Duster_module_Printf(1,"enter %s", __FUNCTION__);
	memset(p_wan_sys_info,0,sizeof(wan_info_t));

	if((p_wan_sys_info->wan_link_status  =   duster_malloc(20)) == NULL)
		goto exit;

	if((p_wan_sys_info->wan_conn_status  =   duster_malloc(20)) == NULL)
		goto exit;

	p_wan_sys_info->wan_conn_type = psm_get_wrapper(PSM_MOD_WAN, NULL, "connect_disconnect");;
	//checkRC(p_wan_sys_info->wan_conn_type);

	if(gDialer2UIPara.ReturnCode == DialerOK || OLED_get_dialer_linkstas())
	{
		strcpy(p_wan_sys_info->wan_link_status,"Connected");
		strcpy(p_wan_sys_info->wan_conn_status,"connected");
		// strcpy(p_wan_sys_info->wan_conn_type,"disabled");
		duster_Printf("%s:link status: %s", __FUNCTION__,"Connected" );

		p_wan_sys_info->ip_address  		=   ConvertIntegertoString(gDialer2UIPara.IPV4_remote_IP);
		p_wan_sys_info->gateway     		=   ConvertIntegertoString(gDialer2UIPara.IPV4_GW);
		p_wan_sys_info->dns1                   =   ConvertIntegertoString(gDialer2UIPara.IPV4_DNS1);
		p_wan_sys_info->dns2                   =   ConvertIntegertoString(gDialer2UIPara.IPV4_DNS2);
		p_wan_sys_info->mask                  =    ConvertIntegertoString(gDialer2UIPara.IPV4_MASK);
	}
	else
	{
		strcpy(p_wan_sys_info->wan_link_status,"Disconnected");
		strcpy(p_wan_sys_info->wan_conn_status,"disconnected");
		//strcpy(p_wan_sys_info->wan_conn_type,"cellular");
		duster_Printf("%s:link status: %s", __FUNCTION__,"Disconnected" );

		p_wan_sys_info->ip_address  		=   NULL;
		p_wan_sys_info->gateway     		=   NULL;
		p_wan_sys_info->dns1                   =   NULL;
		p_wan_sys_info->dns2                  =   NULL;
		p_wan_sys_info->mask                   =   NULL;
	}

	if(!strcmp("Connected", p_wan_sys_info->wan_link_status))
	{
		duster_Printf("%s:wan_link_status =%s", __FUNCTION__, p_wan_sys_info->wan_link_status );
		duster_Printf("%s:ip_address =%s", __FUNCTION__, p_wan_sys_info->ip_address );
		duster_Printf("%s:gateway =%s", __FUNCTION__, p_wan_sys_info->gateway );
		duster_Printf("%s:dns1 =%s", __FUNCTION__, p_wan_sys_info->dns1 );
		duster_Printf("%s:dns2 =%s", __FUNCTION__, p_wan_sys_info->dns2 );
		duster_Printf("%s:mask =%s", __FUNCTION__, p_wan_sys_info->mask );
	}
	if(!strcmp("disabled", p_wan_sys_info->wan_conn_type))
	{
		duster_Printf("%s: disconenct network", __FUNCTION__);
		strcpy(p_wan_sys_info->wan_link_status,"Disconnected");
		strcpy(p_wan_sys_info->wan_conn_status,"disconnected");
	}
	gUI2DialerPara.rssi = get_rssi();


	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "pin_status");
	if(str)
	{
		gDialer2UIPara.pin_status = ConvertStrToInteger(str);
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "sim_status");
	if(str)
	{
		gDialer2UIPara.sim_status = ConvertStrToInteger(str);
		duster_free(str);
		str = NULL;
	}


	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "pin_attempts");
	if(str)
	{
		gDialer2UIPara.pin_attempts = ConvertStrToInteger(str);
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "puk_attempts");
	if(str)
	{
		gDialer2UIPara.puk_attempts = ConvertStrToInteger(str);
		duster_free(str);
		str = NULL;
	}

	Duster_module_Printf(1,"leave %s", __FUNCTION__);
	return 0;

exit:

	if(p_wan_sys_info->wan_link_status!=NULL)
	{
		duster_free(p_wan_sys_info->wan_link_status);
		p_wan_sys_info->wan_link_status = NULL;
	}

	if(p_wan_sys_info->wan_conn_status!=NULL)
	{
		duster_free(p_wan_sys_info->wan_conn_status);
		p_wan_sys_info->wan_conn_status = NULL;
	}
	return -1;
}


UINT32 BatteryChargeStatus()
{
#if !defined(CRANE_WEBUI_SUPPORT)
	if(CHG_state == CHG_State_InProcess)
		return 1;
	else if(CHG_state == CHG_State_Done)
		return 2;
	else
		return 0;
#else
    return 0;
#endif
}

UINT32 BatteryConnectStatus(void)
{
#if !defined(CRANE_WEBUI_SUPPORT)
	if(Bat_connect_state == BAT_LATCHUPED)
		return 0;
	else if(Bat_connect_state == BAT_CONNECTED)
		return 1;
	else
		return 0;
#else
    return 0;
#endif
}
UINT32 CharingStatus(void)
{
#if !defined(CRANE_WEBUI_SUPPORT)
	if(CHG_V_State == VCHG_LOW)
		return 0;
	else
		return 1;
#else
    return 0;
#endif
}



char* UCS2_to_UTF8(unsigned char *ucs2)
{
	unsigned short ucs2_code = 0;
	int len = 0, i =0, j = 0;
	int ucs2_count = 0;
	int nbytes = 0;
	unsigned char bytes[4] = {0};
	char *utf8 = NULL;
	if(ucs2 == NULL)
	{
		return NULL;
	}

	ucs2_count = strlen((char *)ucs2);
	if(ucs2_count <= 0 && ucs2_count%2 != 0)
	{
		return NULL;
	}

	utf8 = duster_malloc((strlen((char *)ucs2)/2) * 3  + 1);
	if(utf8 == NULL)
	{
		return NULL;
	}
	memset(utf8, 0, (strlen((char *)ucs2)/2) * 3  + 1);

	for(i=0; i< ucs2_count; i= i+2)
	{
		ucs2_code = (ucs2[i]<<8) + ucs2[i+1];

		if(ucs2_code < 0x80)
		{
			bytes[0] = ucs2_code;
			nbytes = 1;
		}
		else if(ucs2_code < 0x800)
		{
			bytes[1] = (ucs2_code & 0x3F) | 0x80;
			bytes[0] = (((ucs2_code << 2) & 0x1F00) | 0xC000) >> 8;
			nbytes = 2;
		}
		else
		{
			//big-end
			bytes[2] = (ucs2_code & 0x3F) | 0x80;
			bytes[1] = (((ucs2_code << 2) & 0x3F00) | 0x8000) >> 8;
			bytes[0] = (((ucs2_code << 4) & 0x0F0000) | 0xE00000) >> 16;
			nbytes = 3;
		}

		for(j=0; j< nbytes; j++)
		{
		    utf8[len] = bytes[j];
			len++;
		}
	}

	utf8[len] = '\0';

	Duster_module_Printf(1,"%s, UTF8 len %d",__FUNCTION__,len);
	return utf8;

}
#if !defined(CRANE_WEBUI_SUPPORT)
char *get_network_name(int NameType, int channel)
{
	char at_str[24] = {'\0'};
	char resp_str[64] = {'\0'};
	int ret = 0;
	char cops_head[16] = {'\0'};
	char mode[16] = {'\0'};
	char format[16] = {'\0'};
	char oper_name[64] = {'\0'};
	UINT32 at_channel  = channel;
	char *ret_str = NULL;

	if(!g_set_atcops_done)
	{
		sprintf(at_str, "AT+COPS=3,%d\r",NameType);
		Duster_module_Printf(1,"at_str %s, NameType is %d",at_str, NameType);

		ret = SendATCMDWaitResp(at_channel, at_str, 150, NULL,1,"+CME ERROR", resp_str, sizeof(resp_str));
		if(ret != 0)
		{
			Duster_module_Printf(1,"send at+cops=3,%d ret code %d",NameType,ret);
			return NULL;
		}
		else
		{
			Duster_module_Printf(1,"send at+cops=3,%d ret %s",NameType,resp_str);
			g_set_atcops_done = 1;
		}
	}

	if(g_set_atcops_done)
	{
		sprintf(at_str, "AT+COPS?\r");
		//ret = send_AT_wait_resp(at_channel, at_str, 150, NULL,1,"+CME ERROR", resp_str);
		ret = SendATCMDWaitResp(at_channel, at_str, 150, "+COPS:",1,"+CME ERROR", resp_str, sizeof(resp_str));
		if(ret != 0)
		{
			Duster_module_Printf(1,"send at+cops? ret code %d",ret);
			return NULL;
		}
		else
		{
			Duster_module_Printf(1,"send at+cops? ret %s",resp_str);
			if (sscanf(resp_str, "%[^:]:%[^,],%[^,],%[^,],%*s", cops_head, mode, format,oper_name) != 4)
			{
				Duster_module_Printf(1,"%s: parse failed",__FUNCTION__);
				return NULL;
			}
			else
			{
				Duster_module_Printf(1,"%s: oper_name %s",__FUNCTION__,oper_name);
				/*skip ' '*/
				CurSelectNWMode = atoi(mode + 1);
				Duster_module_Printf(1,"%s: mode is %s %d",__FUNCTION__,mode+1, CurSelectNWMode);
				if(strcmp(oper_name,"\"\"") == 0)
				{
					Duster_module_Printf(1,"%s: invalid oper_name",__FUNCTION__);
					return NULL;
				}
				ret_str = duster_malloc(strlen(oper_name) + 1);
				if(ret_str == NULL)
				{
					Duster_module_Printf(1,"%s: malloc failed",__FUNCTION__);
					return NULL;
				}
				else
				{
					memset(ret_str,'\0', strlen(oper_name) + 1);
					memcpy(ret_str, oper_name+1, strlen(oper_name)-2);
					return ret_str;
				}

			}
		}
	}
	return NULL;
}
#endif
int get_sim_spn(s_spn_info *spn_info, int at_channel)
{
	char at_str[64] = {'\0'};
	char resp_str[64] = {'\0'};
	int ret = 0;
	char tmp[64] = {'\0'};
	char temp[3] = {'\0'};
	int i = 0;
	char *p;
	char acsii_char;
	char *endptr = NULL;

	Duster_module_Printf(1,"%s Enter", __FUNCTION__);
	if(!g_get_spn_done)
	{
		if(is_usim_card())
			sprintf(at_str, "AT+CRSM=176,28486,0,0,17,,3F007FFF\r");
		else
			sprintf(at_str, "AT+CRSM=176,28486,0,0,17,,3F007F20\r");

		ret = SendATCMDWaitResp(at_channel, at_str, 150, "+CRSM",1,"+CME ERROR", resp_str, sizeof(resp_str));
		if(ret != 0)
		{
			Duster_module_Printf(1,"send AT+CRSM to get SPN failed %d",ret);
			return -1;
		}
		else
		{
			Duster_module_Printf(1,"get SPN ret %s",resp_str);
			{
				if(sscanf(resp_str,"%*[^:]:%*[^,],%*[^,],%s",tmp) != 1)
				{
					Duster_module_Printf(1,"parse SPN failed");
					Duster_module_Printf(1,"%s Leave", __FUNCTION__);
					return -1;
				}
				p = tmp;
				if (*p == '"')
				    p++;
				memcpy(temp,p,2);
				p += 2;
				temp[2] = '\0';
				Duster_module_Printf(1,"SPN dis_cond %d",atoi(temp));
				spn_info->dis_cond = atoi(temp);
				while(i<16)
				{
					memcpy(temp,p,2);
					temp[2] = '\0';
					if(strcmp(temp,"FF") == 0)
						break;
					spn_info->spn_exist = 1;
					acsii_char = (char)strtoul(temp,&endptr,16);
					spn_info->spn_name[i] = acsii_char;
					Duster_module_Printf(1,"%d %c",acsii_char,acsii_char);
					i++;
					p = p+2;
				}
				g_get_spn_done = 1;
			}
		}
	}
	Duster_module_Printf(1,"%s Leave", __FUNCTION__);
	return 0;
}

BOOL IsMatchPlmn()
{
	int cur_status;

    #if defined(CRANE_WEBUI_SUPPORT)
    cur_status = get_current_reg_status(0);
    #else
	cur_status = get_current_reg_status();
	#endif
	if(cur_status == CIMM_REGSTATUS_ROAMING)
	{
		return FALSE;
	}
	else
		return TRUE;
}

int get_network_display_rule(s_spn_info * spn_info)
{
	int rule;
	if(!g_get_spn_done)
	{
		rule = RULE_SHOW_PLMN;// PLMN
		return rule;
	}
	if(spn_info == NULL)
	{
		rule = RULE_SHOW_PLMN;// PLMN
		return rule;
	}
	Duster_module_Printf(1,"%s: spn_info dis_cond %d", __FUNCTION__,spn_info->dis_cond);
	if(spn_info->spn_exist == 0)
	{
		rule = RULE_SHOW_PLMN;// PLMN
	}
	else if(IsMatchPlmn())
	{

		rule = RULE_SHOW_SPN;//SPN
		if((spn_info->dis_cond & 0x01) == 0x01)
		{
			rule |= RULE_SHOW_PLMN;
		}
	}
	else
	{
		rule = RULE_SHOW_PLMN;//PLMN
		if((spn_info->dis_cond & 0x02) == 0x00)
		{
			rule |= RULE_SHOW_SPN;
		}
	}

	return rule;
}
char *get_plmn_name_imsi()
{
#define PLMN_NAME_SIZE 32
	char *imsi_plmn_name = NULL;
	char tmp[12] = {'\0'};
	imsi_plmn_name = duster_malloc(PLMN_NAME_SIZE);

	if(imsi_plmn_name == NULL)
		return NULL;

	if((IMSI_APN != NULL) &&(IMSI_APN->apn_info_list != NULL) && (IMSI_APN->apn_info_list->oper_name != NULL))
	{
		Duster_module_Printf(1,"%s: IMSI matched oper_name %s", __FUNCTION__,IMSI_APN->apn_info_list->oper_name);
		strncpy(imsi_plmn_name,IMSI_APN->apn_info_list->oper_name,PLMN_NAME_SIZE);
	}
	else if((strlen(IMSI) > 5))
	{
		memset(tmp, 0,	12);
		memcpy(tmp,	IMSI,	3 + gMncLen);
		strncpy(imsi_plmn_name,tmp,PLMN_NAME_SIZE);
	}
	else if((strlen(IMSI) < 5))
	{
		strncpy(imsi_plmn_name,"NA",PLMN_NAME_SIZE);
	}

	return imsi_plmn_name;

}

char *get_network_name_webui(int roaming, int type)
{
#define PLMN_NAME_SIZE 32
	int rule;
	char *register_plmn = NULL;
	char *ret_plmn_name = NULL;
	char *plmn_name = NULL;
	char ucs2_string[16] = {0};
	int  ret = 0;

	if(!g_get_spn_done)
	{
		memset(&SPN_INFO,	0,	sizeof(s_spn_info));
	}
	get_sim_spn(&SPN_INFO,AT_WEBUI_CHANNEL);
	rule = get_network_display_rule(&SPN_INFO);
	Duster_module_Printf(1,"%s display rule %d", __FUNCTION__,rule);
	register_plmn = get_network_name(0,AT_WEBUI_CHANNEL);
	if(register_plmn)
		Duster_module_Printf(1,"%s registered PLMN %s", __FUNCTION__,register_plmn);

	ret_plmn_name = duster_malloc(PLMN_NAME_SIZE);
	if(ret_plmn_name == NULL)
	{
		Duster_module_Printf(1,"%s [ERROR] malloc failed", __FUNCTION__);
		if (register_plmn !=NULL) duster_free(register_plmn);
		return NULL;
	}
	if(roaming == ROAMING_TYPE)
	{
		if((rule & 0x02) == RULE_SHOW_SPN && type == NETWORK_NAME_TYPE)
		{
			Duster_module_Printf(1,"%s RULE_SHOW_SPN", __FUNCTION__);
			if(SPN_INFO.spn_exist)
			{

				Duster_module_Printf(1,"%s SPN file exist", __FUNCTION__);
				if(SPN_INFO.spn_name[0] == 0x80)
				{
					Duster_module_Printf(1,"%s UCS2 encoded SPN file", __FUNCTION__);
					memcpy(ucs2_string, &SPN_INFO.spn_name[1], 15);
					duster_free(ret_plmn_name);
					ret_plmn_name = NULL;
					ret_plmn_name = UCS2_to_UTF8((unsigned char *)ucs2_string);
				}
				else
					strncpy(ret_plmn_name,SPN_INFO.spn_name,PLMN_NAME_SIZE);
			}
			else
			{

				Duster_module_Printf(1,"%s SPN file NOT exist", __FUNCTION__);

				if(register_plmn)
				{
					Duster_module_Printf(1,"%s: register_plmn %s", __FUNCTION__,register_plmn);
					strncpy(ret_plmn_name,register_plmn,PLMN_NAME_SIZE);
				}
				else
				{
					if(ret_plmn_name)
					{
						duster_free(ret_plmn_name);
						ret_plmn_name = NULL;
					}
				}
			}
		}
		else
		{
			Duster_module_Printf(1,"%s RULE_SHOW_PLMN", __FUNCTION__);
			if(register_plmn)
			{
				Duster_module_Printf(1,"%s: register_plmn %s", __FUNCTION__,register_plmn);
				strncpy(ret_plmn_name,register_plmn,PLMN_NAME_SIZE);
			}
			else
			{
				if(ret_plmn_name)
				{
					duster_free(ret_plmn_name);
					ret_plmn_name = NULL;
				}
			}
		}
	}
	else
	{
		if((rule & 0x01) == RULE_SHOW_PLMN && type == ROAMING_NETWORK_NAME_TYPE)
		{
			Duster_module_Printf(1,"%s RULE_SHOW_PLMN", __FUNCTION__);
			if(register_plmn)
			{
				strncpy(ret_plmn_name,register_plmn,PLMN_NAME_SIZE);
				Duster_module_Printf(1,"%s: register_plmn %s", __FUNCTION__,register_plmn);
			}
			else
			{
				if(ret_plmn_name)
				{
					duster_free(ret_plmn_name);
					ret_plmn_name = NULL;
				}
			}
		}
		else
		{
			if(SPN_INFO.spn_exist)
			{

				Duster_module_Printf(1,"%s SPN file exist", __FUNCTION__);
					if(SPN_INFO.spn_name[0] == 0x80)
				{
					Duster_module_Printf(1,"%s UCS2 encoded SPN file", __FUNCTION__);
					memcpy(ucs2_string, &SPN_INFO.spn_name[1], 15);
					duster_free(ret_plmn_name);
					ret_plmn_name = NULL;
					ret_plmn_name = UCS2_to_UTF8((unsigned char *)ucs2_string);
				}
				else
					strncpy(ret_plmn_name,SPN_INFO.spn_name,PLMN_NAME_SIZE);
			}
			else
			{

				Duster_module_Printf(1,"%s SPN file NOT exist", __FUNCTION__);

				if(register_plmn)
				{
					Duster_module_Printf(1,"%s: register_plmn %s", __FUNCTION__,register_plmn);
					strncpy(ret_plmn_name,register_plmn,PLMN_NAME_SIZE);
				}
				else
				{
					if(ret_plmn_name)
					{
						duster_free(ret_plmn_name);
						ret_plmn_name = NULL;
					}
				}
			}

		}
	}

	if(register_plmn)
	{
		duster_free(register_plmn);
		register_plmn = NULL;
	}

	return ret_plmn_name;
}
extern char * IMSI_Match_Country(void);

char * wan_get_opername(void)
{
	if(gSimStatus == CI_SIM_ST_READY)
	{
		if(IMSI_APN)
		{
			if(IMSI_APN->apn_info_list)
				return IMSI_APN->apn_info_list->oper_name;
			else
				return NULL;
		}
		else
			return NULL;
	}
	else
		return NULL;
}

char *convert_xml_entity(char *str_in)
{
#define MAX_XML_PDATA_SIZE 256
	char *p = str_in;
	char *str_out = NULL;
	char *q;

	if(str_in == NULL)
		return NULL;
	Duster_module_Printf(1,"%s: enter", __FUNCTION__);
	str_out = duster_malloc(MAX_XML_PDATA_SIZE);
	if(str_out == NULL)
		return NULL;
	Duster_module_Printf(1,"%s: in string %s", __FUNCTION__,str_in);
	q = str_out;
	memset(q,0,MAX_XML_PDATA_SIZE);
	while(*p != '\0')
	{
		switch(*p)
		{
		case '<':
			memcpy(q,"&lt;",4);
			q = q+4;
			p++;
			break;
		case '>':
			memcpy(q,"&gt;",4);
			q = q+4;
			p++;
			break;
		case '&':
			memcpy(q,"&amp;",5);
			q = q+5;
			p++;
			break;
		case '"':
			memcpy(q,"&quot;",6);
			q = q+6;
			p++;
			break;
		case '\'':
			memcpy(q,"&apos;",6);
			q = q+6;
			p++;
			break;
		default:
			*q = *p;
			p++;
			q++;
			break;
		}
	}
	*q = '\0';
	Duster_module_Printf(1,"%s: out string %s", __FUNCTION__,str_out);
	return str_out;
}

void set_msisdn_for_webui(char *msisdn)
{
	if(!msisdn)
	{
		Duster_module_Printf(1,"%s: msisdn is NULL", __FUNCTION__);
		return;
	}
	if(strlen(msisdn) > 16)
	{
		Duster_module_Printf(1,"%s: invalid length %d", __FUNCTION__,strlen(msisdn));
		return;
	}

	strcpy(g_msisdn_webui, msisdn);
	Duster_module_Printf(1,"%s: msisdn %s", __FUNCTION__, g_msisdn_webui);
	return;
}

void set_quota_for_webui(int quota)
{
	g_quota = quota;
	Duster_module_Printf(1,"%s: quota %d(MB)", __FUNCTION__, g_quota);
	return;
}

char *get_msisdn_for_webui(void)
{
	if(strlen(g_msisdn_webui) == 0)
	{
		Duster_module_Printf(1,"%s: not get msisdn", __FUNCTION__);
		return NULL;
	}
	else
	{
		Duster_module_Printf(1,"%s: get msisdn %s", __FUNCTION__, g_msisdn_webui);
		return g_msisdn_webui;
	}
}

int get_quota_for_webui(void)
{
	Duster_module_Printf(1,"%s: get quota %d(MB)", __FUNCTION__, g_quota);
	return g_quota;
}

int wan_xml_get(MrvXMLElement*root)
{
	UINT32 current_tick=0;
	wan_info_t *wan_info;
	UINT32 tx_rate_speed, rx_rate_speed=0, quota = 0;
	UINT32 BatteryConnect;
	UINT32 BatteryCharging = 0;
	UINT32 ChargeStatus = 0;
	unsigned char BatteryPercent = 0;
	unsigned int BatteryVolt = 0;
	unsigned int data_mode, nIndex = 0;
	char	tmp[12];
	int reg_status = 0;
	char *oper_name = NULL;
	char *plmn_name = NULL;
	int display_rule = 0;
	char *xml_entity = NULL;
	if((wan_info = (wan_info_t *) duster_malloc(sizeof(wan_info_t))) != NULL)
		memset(wan_info, 0, sizeof(wan_info_t));
	else
		return -1;
	Duster_module_Printf(1,"enter %s", __FUNCTION__);

	if((gSimStatus == CI_SIM_ST_READY) && (IMSI_APN == NULL))
	{
		if(!IMSI_APN)
		{
		    #if defined(CRANE_WEBUI_SUPPORT)
			IMSI_APN = wan_get_apn((CMSimID)0);
			#else
			IMSI_APN = wan_get_apn();
			#endif
		}

	}
	IMSI_Match_Country();
	wan_info_gather(wan_info);

	//mxml_node_t *p, *root1 = NULL;
	MrvXMLElement *pChild = NULL, *pCellularEle = NULL;

	oper_name = get_network_name(0, AT_WEBUI_CHANNEL);

	if(oper_name)
		Duster_module_Printf(1,"%s: oper_name %s", __FUNCTION__,oper_name);

#if defined(CRANE_WEBUI_SUPPORT)
    if(CIMM_REGSTATUS_ROAMING == get_current_reg_status(0))
#else
	if(CIMM_REGSTATUS_ROAMING == get_current_reg_status())
#endif
	{
		Duster_module_Printf(1,"%s in roaming status", __FUNCTION__);
		pChild = MrvFindElement(root,"roaming_network_name");
		if(pChild && oper_name)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			xml_entity = convert_xml_entity(oper_name);
			if(xml_entity)
			{
				MrvAddText(pChild,MrvStrdup(xml_entity,0),1);
				duster_free(xml_entity);
				xml_entity = NULL;
			}
			else
			{

				MrvAddText(pChild,MrvStrdup(oper_name,0),1);
			}
		}
		else
		{
			Duster_module_Printf(1,"%s get oper_name failed", __FUNCTION__);
		}

		pChild = MrvFindElement(root,"network_name");
#ifdef OPERNAME_SACCACHE
		if(pChild && oper_name)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			Duster_module_Printf(1,"%s get oper_name %s", __FUNCTION__,plmn_name);
			xml_entity = convert_xml_entity(oper_name);
			if(xml_entity)
			{
				MrvAddText(pChild,MrvStrdup(xml_entity,0),1);
				duster_free(xml_entity);
				xml_entity = NULL;
			}
			else
			{

				MrvAddText(pChild,MrvStrdup(oper_name,0),1);
			}
		}
		else
		{
			Duster_module_Printf(1,"%s get oper_name failed", __FUNCTION__);
		}
#else
		plmn_name = get_network_name_webui(ROAMING_TYPE,NETWORK_NAME_TYPE);
		if(pChild && plmn_name)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			Duster_module_Printf(1,"%s get plmn_name %s", __FUNCTION__,plmn_name);
			xml_entity = convert_xml_entity(plmn_name);
			if(xml_entity)
			{
				MrvAddText(pChild,MrvStrdup(xml_entity,0),1);
				duster_free(xml_entity);
				xml_entity = NULL;
			}
			else
			{

				MrvAddText(pChild,MrvStrdup(plmn_name,0),1);
			}
		}
		else
		{
			Duster_module_Printf(1,"%s get plmn_name failed", __FUNCTION__);
		}
		if(plmn_name)
		{
			duster_free(plmn_name);
			plmn_name = NULL;
		}
#endif
	}
	else
	{
		pChild = MrvFindElement(root,"network_name");
#ifdef OPERNAME_SACCACHE
		if(pChild && oper_name)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			if(oper_name != NULL)
			{
				xml_entity = convert_xml_entity(oper_name);
				if(xml_entity)
				{
					MrvAddText(pChild,MrvStrdup(xml_entity,0),1);
					duster_free(xml_entity);
					xml_entity = NULL;
				}
				else
				{

					MrvAddText(pChild,MrvStrdup(oper_name,0),1);
				}
			}
			else
				Duster_module_Printf(1,"%s get oper_name failed", __FUNCTION__);
		}
		else
		{
			if(oper_name ==  NULL)
				Duster_module_Printf(1,"%s get oper_name failed", __FUNCTION__);
		}
#else
		plmn_name = get_network_name_webui(NONROAMING_TYPE,NETWORK_NAME_TYPE);
		if(pChild && plmn_name)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			Duster_module_Printf(1,"%s get plmn_name %s", __FUNCTION__,plmn_name);
			xml_entity = convert_xml_entity(plmn_name);
			if(xml_entity)
			{
				MrvAddText(pChild,MrvStrdup(xml_entity,0),1);
				duster_free(xml_entity);
				xml_entity = NULL;
			}
			else
			{

				MrvAddText(pChild,MrvStrdup(plmn_name,0),1);
			}
		}
		else
		{
			if(plmn_name == NULL)
				Duster_module_Printf(1,"%s get plmn_name failed", __FUNCTION__);
		}
		if(plmn_name)
		{
			duster_free(plmn_name);
			plmn_name = NULL;
		}
#endif

		pChild = MrvFindElement(root,"roaming_network_name");
		if(pChild)
		{
#ifdef OPERNAME_SACCACHE
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			if(oper_name != NULL)
			{
				xml_entity = convert_xml_entity(oper_name);
				if(xml_entity)
				{
					MrvAddText(pChild,MrvStrdup(xml_entity,0),1);
					duster_free(xml_entity);
					xml_entity = NULL;
				}
				else
				{

					MrvAddText(pChild,MrvStrdup(oper_name,0),1);
				}
			}
			else
				Duster_module_Printf(1,"%s get oper_name failed", __FUNCTION__);

#else
			plmn_name = get_network_name_webui(NONROAMING_TYPE,ROAMING_NETWORK_NAME_TYPE);
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			if(plmn_name != NULL)
			{
				Duster_module_Printf(1,"%s get plmn_name %s", __FUNCTION__,plmn_name);
				xml_entity = convert_xml_entity(plmn_name);
				if(xml_entity)
				{
					MrvAddText(pChild,MrvStrdup(xml_entity,0),1);
					duster_free(xml_entity);
					xml_entity = NULL;
				}
				else
				{

					MrvAddText(pChild,MrvStrdup(plmn_name,0),1);
				}
			}
			else
				Duster_module_Printf(1,"%s get plmn_name failed", __FUNCTION__);

			if(plmn_name)
			{
				duster_free(plmn_name);
				plmn_name = NULL;
			}
#endif
		}
	}
	if(oper_name)
	{
		duster_free(oper_name);
		oper_name = NULL;
	}
	if(plmn_name)
	{
		duster_free(plmn_name);
		plmn_name = NULL;
	}

	pChild = MrvFindElement(root,"ConnType");
	if(pChild&&wan_info->wan_conn_type)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(wan_info->wan_conn_type,0),1);
	}

	pChild = MrvFindElement(root,"ip");
	if(pChild&&wan_info->ip_address)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(wan_info->ip_address,0),1);
	}

	pChild = MrvFindElement(root,"wan_link_status");
	if(pChild&&wan_info->wan_link_status)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(wan_info->wan_link_status,0),1);
	}

	pChild = MrvFindElement(root,"wan_conn_status");
	if(pChild&&wan_info->wan_conn_status)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(wan_info->wan_conn_status,0),1);
	}

	pChild = MrvFindElement(root,"gateway");
	if(pChild&&wan_info->gateway)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(wan_info->gateway,0),1);
	}


	pChild = MrvFindElement(root,"dns1");
	if(pChild&&wan_info->dns1)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;

		MrvAddText(pChild,MrvStrdup(wan_info->dns1,0),1);
	}


	pChild = MrvFindElement(root,"dns2");
	if(pChild&&wan_info->dns2)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(wan_info->dns2,0),1);
	}


	pChild = MrvFindElement(root,"mask");
	if(pChild&&wan_info->mask)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(wan_info->mask,0),1);
	}


	pChild = MrvFindElement(root,"tx_rate");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);

		tx_rate_speed = td_get_tx_speed();
		itoa(tx_rate_speed,tmp,10);
		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	pChild = MrvFindElement(root,"rx_rate");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);

		rx_rate_speed = td_get_rx_speed();
		itoa(rx_rate_speed,tmp,10);
		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}


	pChild = MrvFindElement(root,"quota");
	if(pChild && get_quota_for_webui() > 0)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);

		quota = get_quota_for_webui();
		itoa(quota,tmp,10);
		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	if(wan_info->wan_link_status!=NULL)
	{
		duster_free(wan_info->wan_link_status);
		wan_info->wan_link_status = NULL;
	}

	if(wan_info->wan_conn_type!=NULL)
	{
		duster_free(wan_info->wan_conn_type);
		wan_info->wan_conn_type = NULL;
	}

	if(wan_info->wan_conn_status!=NULL)
	{
		duster_free(wan_info->wan_conn_status);
		wan_info->wan_conn_status = NULL;
	}

	if(wan_info->ip_address!=NULL)
	{
		duster_free(wan_info->ip_address);
		wan_info->ip_address = NULL;
	}

	if(wan_info->gateway!=NULL)
	{
		duster_free(wan_info->gateway);
		wan_info->gateway = NULL;
	}

	if(wan_info->dns1!=NULL)
	{
		duster_free(wan_info->dns1);
		wan_info->dns1 = NULL;
	}

	if(wan_info->dns2!=NULL)
	{
		duster_free(wan_info->dns2);
		wan_info->dns2 = NULL;
	}
	if(wan_info->mask!=NULL)
	{
		duster_free(wan_info->mask);
		wan_info->mask = NULL;
	}

	if(wan_info!=NULL)
	{
		duster_free(wan_info);
		wan_info = NULL;
	}
#if 0

	/* Add ACT detail information*/
	if(gPSActDetail!=0xFF)
		data_mode = gPSActDetail;
	else
		data_mode = gCsActDetail;
	if( (p = mxmlFindElement(root1, root1, "data_conn_mode", NULL, NULL, MXML_DESCEND_FIRST)) != NULL)
		mxmlNewInteger(p, data_mode);
#endif

	/* Add Battery info for dashborad*/
	BatteryConnect = BatteryConnectStatus();
	BatteryCharging = CharingStatus();
	ChargeStatus = BatteryChargeStatus();
	BatteryPercent = GetBatteryPercent();
	BatteryVolt = GetBatteryVoltage();
	if (ChargeStatus != 0)
	{
		if(BatteryPercent>=100)
			ChargeStatus = 2;
		else
			ChargeStatus = 1;
	}
	Duster_module_Printf(1,"%s: BatteryConnect %d,BatteryCharging %d, ChargeStatus %d, BatteryPercent %d, BatteryVolt %d",__FUNCTION__, BatteryConnect,BatteryCharging,ChargeStatus,BatteryPercent,BatteryVolt);

	pChild = MrvFindElement(root,"Battery_connect");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(BatteryConnect,tmp,10);
		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	pChild = MrvFindElement(root,"Battery_charging");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(BatteryCharging,tmp,10);

		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	pChild = MrvFindElement(root,"Battery_charge");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(ChargeStatus,tmp,10);

		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}
	pChild = MrvFindElement(root,"Battery_voltage");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(BatteryPercent,tmp,10);

		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	FILE *p_fp;
	int rssi=0, sim_status, pin_status, conn_days=0, conn_hours=0, conn_minutes=0, roaming=1;
	int total_conn_days=0, total_conn_hours=0, total_conn_minutes=0;
	long int ppp_conn_time, total_ppp_conn_time, current_time, start_time;
	char current_time_str[20];
	char xmlbuf[4]= {0};

	pCellularEle = MrvFindElement(root,"cellular");
	if(pCellularEle)
	{
		if(gDialer2UIPara.ReturnCode == DialerOK)
		{
			current_tick = OSAGetTicks();
			ppp_conn_time = (current_tick - gDialer2UIPara.connected_tick)/200;
		}
		else
			ppp_conn_time =  0;

		total_ppp_conn_time = ppp_conn_time + gDialer2UIPara.total_connected_tick/200;
		Duster_module_Printf(1,"%s: total_ppp_conn_time = %u, ppp_conn_time = %u, total_connected_tick = %u", __FUNCTION__, total_ppp_conn_time, ppp_conn_time, gDialer2UIPara.total_connected_tick );

		// 1 day = 86400 seconds, 1 hour = 3600 seconds
		if(ppp_conn_time >= 86400)
		{
			conn_days=ppp_conn_time/86400;
			ppp_conn_time=ppp_conn_time%86400;

			pChild = MrvFindElement(pCellularEle,"conn_days");
			if(pChild)
			{
				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;
				memset(tmp, 0,  12);
				itoa(conn_days,tmp,10);
				MrvAddText(pChild,MrvStrdup(tmp,0),1);
			}
		}

		if(ppp_conn_time >= 3600)
		{
			conn_hours=ppp_conn_time/3600;
			ppp_conn_time=ppp_conn_time%3600;
			pChild = MrvFindElement(pCellularEle,"conn_hours");
			if(pChild)
			{
				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;
				memset(tmp, 0,  12);
				itoa(conn_hours,tmp,10);
				MrvAddText(pChild,MrvStrdup(tmp,0),1);
			}
		}
		if(ppp_conn_time >= 60)
		{
			conn_minutes=ppp_conn_time/60;
			ppp_conn_time=ppp_conn_time%60;
			pChild = MrvFindElement(pCellularEle,"conn_minutes");
			if(pChild)
			{
				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;
				memset(tmp, 0,  12);
				itoa(conn_minutes,tmp,10);
				MrvAddText(pChild,MrvStrdup(tmp,0),1);
			}
		}

		pChild = MrvFindElement(pCellularEle,"conn_seconds");
		if(pChild)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			memset(tmp, 0,  12);
			itoa(ppp_conn_time,tmp,10);
			MrvAddText(pChild,MrvStrdup(tmp,0),1);
		}

		if(total_ppp_conn_time >= 86400)
		{
			total_conn_days=total_ppp_conn_time/86400;
			total_ppp_conn_time=total_ppp_conn_time%86400;
			pChild = MrvFindElement(pCellularEle,"conn_days_all");
			if(pChild)
			{
				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;
				memset(tmp, 0,  12);
				itoa(total_conn_days,tmp,10);
				MrvAddText(pChild,MrvStrdup(tmp,0),1);
			}

		}
		if(total_ppp_conn_time >= 3600)
		{
			total_conn_hours=total_ppp_conn_time/3600;
			total_ppp_conn_time=total_ppp_conn_time%3600;

			pChild = MrvFindElement(pCellularEle,"conn_hours_all");
			if(pChild)
			{
				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;
				memset(tmp, 0,	12);
				itoa(total_conn_hours,tmp,10);
				MrvAddText(pChild,MrvStrdup(tmp,0),1);
			}
		}
		if(total_ppp_conn_time >= 60)
		{
			total_conn_minutes=total_ppp_conn_time/60;
			total_ppp_conn_time=total_ppp_conn_time%60;
			pChild = MrvFindElement(pCellularEle,"conn_minutes_all");
			if(pChild)
			{
				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;
				memset(tmp, 0,	12);
				itoa(total_conn_minutes,tmp,10);
				MrvAddText(pChild,MrvStrdup(tmp,0),1);
			}

		}
		pChild = MrvFindElement(pCellularEle,"conn_seconds_all");
		if(pChild)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			memset(tmp, 0,	12);
			itoa(total_ppp_conn_time,tmp,10);
			MrvAddText(pChild,MrvStrdup(tmp,0),1);
		}
		#if defined(CRANE_WEBUI_SUPPORT)
		reg_status = get_current_reg_status(0);
        #else
        reg_status = get_current_reg_status();
        #endif

		gUI2DialerPara.roaming = (reg_status == CIMM_REGSTATUS_ROAMING)?1:0; //  1 --roaming; 0-- non-roaming


		Duster_module_Printf(1,"%s: Current Reg Status is %d, gUI2DialerPara.roaming is %d", __FUNCTION__,reg_status,gUI2DialerPara.roaming);

		pChild = MrvFindElement(pCellularEle,"roaming");
		if(pChild)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			memset(tmp, 0,	12);
			itoa(gUI2DialerPara.roaming,tmp,10);
			MrvAddText(pChild,MrvStrdup(tmp,0),1);
		}
		pChild = MrvFindElement(pCellularEle,"rssi");
		if(pChild)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			memset(tmp, 0,	12);
			itoa(gUI2DialerPara.rssi,tmp,10);
			duster_Printf("%s: rssi is %d %s", __FUNCTION__,gUI2DialerPara.rssi,tmp);
			MrvAddText(pChild,MrvStrdup(tmp,0),1);
		}

		pChild = MrvFindElement(pCellularEle,"select_NW_Mode");
		if(pChild)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			itoa(CurSelectNWMode,tmp,10);
			MrvAddText(pChild,MrvStrdup(tmp,0),1);
		}

	#if 0
		sprintf(xmlbuf,"%d",gDialer2UIPara.pin_status);
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "pin_status", xmlbuf);
		check_SIM_status_simple();

		memset(xmlbuf, 0 , 4);
		sprintf(xmlbuf,"%d",gDialer2UIPara.sim_status);
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "sim_status", xmlbuf);
	#endif
	}
	/*
		pChild = MrvFindElement(root,"network_name");

		if(pChild && (IMSI_APN != NULL) &&(IMSI_APN->apn_info_list != NULL) && (IMSI_APN->apn_info_list->oper_name != NULL))
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			MrvAddText(pChild,MrvStrdup(IMSI_APN->apn_info_list->oper_name,0),1);
		}
		else if(pChild && (strlen(IMSI) > 5))
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			memset(tmp, 0,	12);
			memcpy(tmp,	IMSI,	5);
			MrvAddText(pChild,MrvStrdup(tmp,0),1);
		}
		else if(pChild && (strlen(IMSI) < 5))
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			MrvAddText(pChild,MrvStrdup("NA",0),1);
		}
	*/

	// Add IMSI tag
	pChild = MrvFindElement(root,"IMSI");
	if(pChild && strlen(IMSI) > 0)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;

		MrvAddText(pChild,MrvStrdup(IMSI,0),1);
	}

	if(UpdateVersionFlag)
	{
		pChild = MrvFindElement(root,"version_flag");
		if(pChild)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			memset(tmp, 0,	12);
			#if 0
			CPUartLogPrintf("%s: version data is %X", __FUNCTION__, *(UINT32 *)PLATFORM_MODE_ADDRESS);
			if(*(UINT32 *)PLATFORM_MODE_ADDRESS == PLATFORM_5MODE_LTG_VER)
			{
				sprintf(tmp, "1");
				psm_set_wrapper(PSM_MOD_WAN, NULL, "version_flag","1");
			}

			else if(*(UINT32 *)PLATFORM_MODE_ADDRESS == PLATFORM_5MODE_LWG_VER)
			{
				sprintf(tmp, "0");
				psm_set_wrapper(PSM_MOD_WAN, NULL, "version_flag","0");
			}
			else if(*(UINT32 *)PLATFORM_MODE_ADDRESS == PLATFORM_3MODE_LWG_VER)
			{
				sprintf(tmp, "2");
				psm_set_wrapper(PSM_MOD_WAN, NULL, "version_flag","2");
			}
			else if(*(UINT32 *)PLATFORM_MODE_ADDRESS == PLATFORM_3MODE_LTG_VER)
			{
				sprintf(tmp, "3");
				psm_set_wrapper(PSM_MOD_WAN, NULL, "version_flag","3");
			}
			else
			{
				sprintf(tmp, "0");
				Duster_module_Printf(1, "wrong value in version_flag: %x", *(UINT32 *)PLATFORM_MODE_ADDRESS);
			}
			#endif

			sprintf(tmp, "0");
			psm_set_wrapper(PSM_MOD_WAN, NULL, "version_flag","0");

			MrvAddText(pChild,MrvStrdup(tmp,0),1);
			UpdateVersionFlag = FALSE;
		}
	}
	/*add xml node to idicate the network regist status*/
	pChild = MrvFindElement(root,	"NW_register_status");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
#if 0
		if(CgregReady || CeregReady)
		{
			MrvAddText(pChild,MrvStrdup("1",0),1);
		}
		else
		{
			MrvAddText(pChild,MrvStrdup("0",0),1);
		}
#endif

		Duster_module_Printf(1,"%s: current reg status %d", __FUNCTION__, reg_status);
		memset(tmp, 0,	12);
		itoa(reg_status,tmp,10);
		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	pChild = MrvFindElement(root,"rsrp");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(engi_RSRP,tmp,10);

		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	pChild = MrvFindElement(root,"rssi");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(engi_RSSI,tmp,10);

		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	pChild = MrvFindElement(root,"sinr");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(engi_SINR,tmp,10);

		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	pChild = MrvFindElement(root,"cellid");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(engi_CellID,tmp,10);

		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}

	pChild = MrvFindElement(root,"cell_id");
	if(pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmp, 0,	12);
		itoa(getCellID(1),tmp,10);

		MrvAddText(pChild,MrvStrdup(tmp,0),1);
	}
	Duster_module_Printf(1,"leave %s", __FUNCTION__);
	return 0;
}
static char imei_sv[32] = {0};
static int CNUM_TRY_NUM = 5;
static int CGSN_TRY_NUM = 5;
void setAPPinfo(MrvXMLElement*root)
{
#define AT_RES_MAX_SIZE 256	/*+CNUM returns too long, make cgitask data abort happened*/
	MrvXMLElement *pChild = NULL,*pCellular = NULL;
	CHAR APN[20]= {0};
	char *str = NULL;
	int nIndex = 0;
	char tmpbuf[12];
	unsigned char netmode;	/*porting from oled_ui.c*/
	char iccid_str[21] = {'\0'};
	int i = 0;
	int iccid_ready = 0;
	char res_str[AT_RES_MAX_SIZE] = {'\0'};//make sure the size is enough
	int ret;
	int imei_sv_done = 0;
	int msisdn_done = 0;
	INT32 sysMainMode, sysMode;


	Duster_module_Printf(1,"enter %s", __FUNCTION__);
	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "ISP_name");
	pCellular = MrvFindElement(root ,"Cellular");

	getSysMode(1, &sysMainMode, &sysMode);

	if(str&&pCellular)
	{
		pChild = MrvFindElement(pCellular , "ISP_name");
		if(pChild)
		{
			if(strlen(str)>0 && strlen(str)<128)
			{
				duster_Printf("%s:ISP_name =%s", __FUNCTION__, str );

				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;
				MrvAddText(pChild,MrvStrdup(str,0),1);
			}
		}
		duster_free(str);
		str = NULL;
	}
	if(str != NULL) duster_free(str);
	pChild = MrvFindElement(root , "sys_mode");
	if(pChild)
	{

		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmpbuf,	0,	12);
		itoa(sysMainMode,tmpbuf,10);
		MrvAddText(pChild,MrvStrdup(tmpbuf,0),1);
	}


	pChild = MrvFindElement(root , "sys_submode");
	if(pChild)
	{

		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		memset(tmpbuf,	0,	12);
		itoa(sysMode,	tmpbuf,	10);
		MrvAddText(pChild,MrvStrdup(tmpbuf,0),1);
	}
	Duster_module_Printf(1,"%s: sys_mode sys_submode %d %d", __FUNCTION__,sysMainMode,sysMode);
	pChild = MrvFindElement(root , "ISP");
	if(pChild)
	{

		duster_Printf("%s: APN length is %d", __FUNCTION__,  strlen(gUI2DialerPara.APN) );
		if( strlen(gUI2DialerPara.APN) > 2 && strlen(gUI2DialerPara.APN) < 20)
			memcpy(APN, &gUI2DialerPara.APN[1], strlen(gUI2DialerPara.APN)-2);
		duster_Printf("%s:APN =%s", __FUNCTION__, APN );

		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(APN,0),1);
	}


	pChild = MrvFindElement(root , "IMEI");
	if(pChild)
	{
#ifdef QUECTEL_PROJECT_CUST
		memset(res_str, 0, sizeof(res_str));
        char ret_str[AT_RES_MAX_SIZE] = {'\0'};
		ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "AT+CGSN\r", 150, NULL,1,"+CME ERROR", res_str, sizeof(res_str));
    	sscanf(res_str, "\"%255[^\"]\"", ret_str);
		if(0 == ret)
		{
			duster_Printf("%s: AT+CGSN response res_str %s", __FUNCTION__, res_str);
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			MrvAddText(pChild,MrvStrdup(ret_str,0),1);
		}
#endif
#ifndef QUECTEL_PROJECT_CUST
		/**
		 * note: the webui is not displaying imei, perhaps it's just a temporary annotation
		 */
		if(strlen(IMEI_WEB)>0 && strlen(IMEI_WEB)<17)
		{
			duster_Printf("%s:web imei  =%s", __FUNCTION__, IMEI_WEB );
		}

		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(IMEI_WEB,0),1);
#endif
	}

	pChild = MrvFindElement(root, "LWG_flag");
	if(pChild)
	{
		duster_Printf("%s: LWG_flag", __FUNCTION__);
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		if( PlatformIsLwgVersion() == TRUE)      //LWG
		{
			sprintf(tmpbuf,"1");
			duster_Printf("%s: LWG_flag 1", __FUNCTION__);
			MrvAddText(pChild,MrvStrdup(tmpbuf,0),1);
		}
		else
		{
			sprintf(tmpbuf,"0");
			duster_Printf("%s: LWG_flag 0", __FUNCTION__);
			MrvAddText(pChild,MrvStrdup(tmpbuf,0),1);
		}
	}
#ifdef QUECTEL_PROJECT_CUST
	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "NW_mode");
	pChild = MrvFindElement(root , "NW_mode");
	if(str && pChild)
	{
		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
		{
			MrvDeleteNode(&pChild->pEntries[nIndex]);
		}
		if (pChild->pEntries)
			duster_free(pChild->pEntries);
		pChild->pEntries = NULL;
		pChild->nMax = 0;
		pChild->nSize = 0;
		MrvAddText(pChild,MrvStrdup(str,0),1);

		duster_free(str);
		str = NULL;
	}
#endif
	if(gDialer2UIPara.CheckSIMDone)
	{
		char tmp[21] = {'\0'};
		pChild = MrvFindElement(root , "ICCID");
		if(pChild)
		{
			snprintf(iccid_str,21,"%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x",\
			         ICCID[0],ICCID[1],ICCID[2],ICCID[3],\
			         ICCID[4],ICCID[5],ICCID[6],ICCID[7],\
			         ICCID[8],ICCID[9]);

			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;

			for(i=0; i<20; i++)
			{
				if(*(iccid_str+i) != '0')
				{
					iccid_ready = 1;
					duster_Printf("%s:iccid %s", __FUNCTION__, iccid_str);
					break;
				}
			}
			//
			if(iccid_ready)
			{
				for(i=0; i<10; i++)
				{
					tmp[2*i] = *(iccid_str+2*i+1);
					tmp[2*i+1] = *(iccid_str+2*i);
				}
				duster_Printf("%s:web iccid %s", __FUNCTION__, tmp);
			}
			MrvAddText(pChild,MrvStrdup(tmp,0),1);

		}
	}
	if(gDialer2UIPara.CheckSIMDone)
	{
		char *string0 = NULL;
		char string1[42] = {0};
		char string2[42] = {0};
		char string3[4] = {0};

		if(strlen(imei_sv) > 0)
		{
			pChild = MrvFindElement(root , "IMEI_SV");
			if(pChild)
			{
				duster_Printf("%s: imei_sv:%s", __FUNCTION__, imei_sv);
				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;
				MrvAddText(pChild,MrvStrdup(imei_sv,0),1);
			}
		}
		else
		{
			ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "AT*CGSN?\r", 150, NULL,1,"+CME ERROR", res_str, sizeof(res_str));
			if(ret == 0)
			{
				duster_Printf("%s: AT*CGSN? response string %s", __FUNCTION__, res_str);
				string0 = strtok(res_str,"\r\n");
				duster_Printf("%s: AT*CGSN? response string0 %s", __FUNCTION__, string0);
				if(strlen(string0)> 0)//
				{
					// IMEI-SV
					pChild = MrvFindElement(root , "IMEI_SV");
					if(pChild)
					{
						duster_Printf("%s: imei_sv:%s", __FUNCTION__, string0);
						for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
						{
							MrvDeleteNode(&pChild->pEntries[nIndex]);
						}
						if (pChild->pEntries)
							duster_free(pChild->pEntries);
						pChild->pEntries = NULL;
						pChild->nMax = 0;
						pChild->nSize = 0;
						MrvAddText(pChild,MrvStrdup(string0,0),1);
						strncpy(imei_sv,	string0,	32);
						imei_sv_done = 1;
					}

				}
			}
			else
			{
				duster_Printf("%s: AT*CGSN? response error %d", __FUNCTION__, ret);
				if(CGSN_TRY_NUM > 0)
				{
					CGSN_TRY_NUM--;
				}
				else if(CGSN_TRY_NUM == 0)
				{
					memcpy(imei_sv,	"Unknow",	6);
				}
			}
		}
		// MSISDN
		if(get_msisdn_for_webui() != NULL)
		{

			pChild = MrvFindElement(root , "MSISDN");
			if(pChild)
			{
				duster_Printf("%s: MSISDN:%s", __FUNCTION__, get_msisdn_for_webui());
				for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
				{
					MrvDeleteNode(&pChild->pEntries[nIndex]);
				}
				if (pChild->pEntries)
					duster_free(pChild->pEntries);
				pChild->pEntries = NULL;
				pChild->nMax = 0;
				pChild->nSize = 0;

				MrvAddText(pChild,MrvStrdup(get_msisdn_for_webui(),0),1);
			}
		}
		else
		{
			memset(res_str,'\0',AT_RES_MAX_SIZE);
			ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "AT+CNUM\r", 150, "+CNUM",1,"+CME ERROR", res_str, sizeof(res_str));
			if(ret == 0)
			{
				duster_Printf("%s:AT+CNUM response %s", __FUNCTION__, res_str);
				if(strncmp(res_str,"+CNUM",5) == 0)
				{
					/*
					string0 = strtok(res_str,":");
					string1 = strtok(NULL,",");//name
					string2 = strtok(NULL,",");//number
					string3 = strtok(NULL,"\r\n");//type
					*/
					if (sscanf(res_str, "%*[^:]:%[^,],%[^,],%s", string1, string2, string3) == 3)
					{
						duster_Printf("%s: AT+CNUM response name %s, number %s, type: %s", __FUNCTION__, string1,string2,string3);
						duster_Printf("strlen(string2) is %d", strlen(string2));
					}

					pChild = MrvFindElement(root , "MSISDN");
					if(pChild)
					{
						duster_Printf("%s: MSISDN:%s", __FUNCTION__, string2);
						for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
						{
							MrvDeleteNode(&pChild->pEntries[nIndex]);
						}
						if (pChild->pEntries)
							duster_free(pChild->pEntries);
						pChild->pEntries = NULL;
						pChild->nMax = 0;
						pChild->nSize = 0;
						if(strlen(string2) != 0)
						{
							MrvAddText(pChild,MrvStrdup(string2,0),1);
							set_msisdn_for_webui(string2);
						}
						else
						{
							MrvAddText(pChild,MrvStrdup("Unknown",0),1);
							set_msisdn_for_webui("Unknown");
						}
						msisdn_done = 1;
					}

				}
			}
			else
			{
				if(CNUM_TRY_NUM > 0)
				{
					CNUM_TRY_NUM--;
				}
				else if(CNUM_TRY_NUM == 0)
				{
					set_msisdn_for_webui("Unknown");
				}
				duster_Printf("%s:AT+CNUM response error %d", __FUNCTION__, ret);
			}

			if(imei_sv_done && msisdn_done)
				gDialer2UIPara.GetSIMInfoDone = 1;
		}
	}
	else
	{
		duster_Printf("%s: SIM not ready or no need to MSISDN and IMEI-SV again", __FUNCTION__);
	}
	Duster_module_Printf(1,"leave %s", __FUNCTION__);
}

void wan_get_pdp_rule_name(int conn_num, char *rule_name)
{

}


void free_v4info(Duster_UI_PDP_Context *pdp_context)
{
	Duster_module_Printf(1,"enter %s", __FUNCTION__);

	if(pdp_context->ipv4)
	{
		duster_free(pdp_context->ipv4);
		pdp_context->ipv4 = NULL;
	}
	if(pdp_context->v4dns1)
	{
		duster_free(pdp_context->v4dns1);
		pdp_context->v4dns1 = NULL;
	}
	if(pdp_context->v4dns2)
	{
		duster_free(pdp_context->v4dns2);
		pdp_context->v4dns2 = NULL;
	}
	if(pdp_context->v4netmask)
	{
		duster_free(pdp_context->v4netmask);
		pdp_context->v4netmask = NULL;
	}
	if(pdp_context->v4gateway)
	{
		duster_free(pdp_context->v4gateway);
		pdp_context->v4gateway = NULL;
	}

	Duster_module_Printf(1,"leave %s", __FUNCTION__);
}

void free_v6info(Duster_UI_PDP_Context *pdp_context)
{
	Duster_module_Printf(1,"enter %s", __FUNCTION__);
	if(pdp_context->ipv6)
	{
		duster_free(pdp_context->ipv6);
		pdp_context->ipv6 = NULL;
	}
	if(pdp_context->g_ipv6)
	{
		duster_free(pdp_context->g_ipv6);
		pdp_context->g_ipv6 = NULL;
	}
	if(pdp_context->v6dns1)
	{
		duster_free(pdp_context->v6dns1);
		pdp_context->v6dns1 = NULL;
	}
	if(pdp_context->v6dns2)
	{
		duster_free(pdp_context->v6dns2);
		pdp_context->v6dns2 = NULL;
	}
	if(pdp_context->v6netmask)
	{
		duster_free(pdp_context->v6netmask);
		pdp_context->v6netmask = NULL;
	}
	if(pdp_context->v6gateway)
	{
		duster_free(pdp_context->v6gateway);
		pdp_context->v6gateway = NULL;
	}
	Duster_module_Printf(1,"leave %s", __FUNCTION__);

}

int get_current_conn_second(int conn_num, int ticks)
{
	unsigned int current_ticks = OSAGetTicks();
	int ret_sec = 0;

	if(current_ticks - ticks > 0)
	{
		ret_sec = (current_ticks - ticks)/200;
	}
	else
		ret_sec = 0;

	return ret_sec;
}

int get_total_conn_second(int conn_num,int total_ticks, int current_ticks)
{
	int ret_sec = 0;
	int current_conn_tick = (OSAGetTicks() - current_ticks);
	switch(conn_num)
	{
	case 1:
		gDialer2UIPara.connected_tick1 = total_ticks + current_conn_tick;
		//if(gDialer2UIPara.connected_tick1 > 0)
			ret_sec = (gDialer2UIPara.connected_tick1 + gDialer2UIPara.total_connected_tick1)/200;
		//else
		//	ret_sec = 0;
		break;
	case 2:
		gDialer2UIPara.connected_tick2 = total_ticks + current_conn_tick;
		//if(gDialer2UIPara.connected_tick2 > 0)
			ret_sec = (gDialer2UIPara.connected_tick2 + gDialer2UIPara.total_connected_tick2)/200;
		//else
		//	ret_sec = 0;
		break;
	case 3:
		gDialer2UIPara.connected_tick3 = total_ticks + current_conn_tick;
		//if(gDialer2UIPara.connected_tick3 > 0)
			ret_sec = (gDialer2UIPara.connected_tick3 + gDialer2UIPara.total_connected_tick3)/200;
		//else
		//	ret_sec = 0;
		break;
	case 4:
		gDialer2UIPara.connected_tick4 = total_ticks + current_conn_tick;
		//if(gDialer2UIPara.connected_tick4 > 0)
			ret_sec = (gDialer2UIPara.connected_tick4 + gDialer2UIPara.total_connected_tick4)/200;
		//else
		//	ret_sec = 0;
		break;
	case 5:
		gDialer2UIPara.connected_tick5 = total_ticks + current_conn_tick;
		//if(gDialer2UIPara.connected_tick5 > 0)
			ret_sec = (gDialer2UIPara.connected_tick5 + gDialer2UIPara.total_connected_tick5)/200;
		//else
		//	ret_sec = 0;
		break;
	case 6:
		gDialer2UIPara.connected_tick6 = total_ticks + current_conn_tick;
		//if(gDialer2UIPara.connected_tick6 > 0)
			ret_sec = (gDialer2UIPara.connected_tick6 + gDialer2UIPara.total_connected_tick6)/200;
		//else
		//	ret_sec = 0;
		break;
	default:
		break;
	}
	return ret_sec;
}



int get_total_link_time()
{
	LinkStatus_Context * pdp_context = NULL;
	int totalconntime = 0;

	pdp_context = CM_Get_LinkStatus(1);

	if(pdp_context != NULL)
	{
		 Duster_module_Printf(1, "%s: connect_status %d", __FUNCTION__, pdp_context->connect_status);
		 if(pdp_context->connect_status == 1)
		 {
		 	totalconntime = get_total_conn_second(1, pdp_context->pdpinfo->total_connected_tick, pdp_context->pdpinfo->connected_tick);
			Duster_module_Printf(1,"%s: total conn time %d", __FUNCTION__, totalconntime);
		 }
		 else
		 {
		 	totalconntime = get_total_conn_second(1, pdp_context->pdpinfo->total_connected_tick, 0);
			Duster_module_Printf(1,"%s:[] total conn time %d",__FUNCTION__, totalconntime);
		 }
		 free_link_status(pdp_context);
		 return totalconntime;

	}
	totalconntime = get_total_conn_second(1, 0, 0);
	Duster_module_Printf(1,"%s: total conn time %d",__FUNCTION__, totalconntime);
	return totalconntime;
}


void wan_get_pdp_context()
{
#define ONE_PDP_LENGTH 384
	char *str = NULL;
	char fd,rd;
	char pdp_context_buf[2048] = {'\0'};
	char tmp_buf[ONE_PDP_LENGTH] = {'\0'};
	LinkStatus_Context * pdp_context = NULL;
	Duster_UI_PDP_Context duster2UIpdp;
	int connection_num = 0;
	lte_ipv6_addr ipv6_address;
	int auto_apn = 0;
	int web_dns_enable= 0;
	char *web_dns1 = NULL, *web_dns2 = NULL;
	memset(&ipv6_address,0,sizeof(lte_ipv6_addr));
	Duster_module_Printf(1,"enter %s", __FUNCTION__);
	memset(&duster2UIpdp, 0, sizeof(Duster_UI_PDP_Context));
	duster_call_module_get_delimiters(PSM_MOD_WAN, &fd, &rd);

	auto_apn = getAutoApn();
	duster_Printf("%s: auto_apn is %d", __FUNCTION__, auto_apn);

	str = psm_get_wrapper(PSM_MOD_statistics, "Cellular", "total_conn_tick");
	if(str!= NULL && strncmp(str,"",1))
	{
		gDialer2UIPara.total_connected_tick = ConvertStrToInteger(str);
		duster_Printf("%s: gDialer2UIPara.total_connected_tick =%d, %u",__FUNCTION__,gDialer2UIPara.total_connected_tick,gDialer2UIPara.total_connected_tick);
	}
	if(str != NULL)
	{
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_LAN, NULL, "dns_enable");
	if(str)
	{
		if(!strncmp(str,"1",1))
		{
			web_dns_enable = 1;
		}
		duster_free(str);
		str = NULL;
	}

	for(connection_num = 1; connection_num <= MAX_MO_PDP_NUMBER || (connection_num >=MIN_CONNUM_CREATBY_NETWORK && connection_num < MIN_CONNUM_CREATBY_NETWORK+MAX_MT_PDP_NUMBER); connection_num++)
	{
		memset(tmp_buf,'\0',ONE_PDP_LENGTH);
		memset(&duster2UIpdp, 0, sizeof(Duster_UI_PDP_Context));
		pdp_context = CM_Get_LinkStatus(connection_num);
		if(pdp_context != NULL)
		{
			memset(duster2UIpdp.rulename,'\0',64);
			/*rjin, 20140521, For LTE created PDP, if no PDP name using default name, start*/
			if(strlen(pdp_context->PDP_name) == 0 && pdp_context->pdpinfo->PDP_Type == 1)
			{
				if(strlen(g_MT_pPDP_name) > 0)
				{
					strcpy(duster2UIpdp.rulename,g_MT_pPDP_name);
				}
				else
				{
					// default primary LTE
					strcpy(duster2UIpdp.rulename,"MT-LTE");
				}
			}
			else if(strlen(pdp_context->PDP_name) == 0 && pdp_context->pdpinfo->PDP_Type == 0)
			{
				if(strlen(g_MT_sPDP_name) > 0)
				{
					//dedicated primary LTE
					strcpy(duster2UIpdp.rulename,g_MT_sPDP_name);
				}
				else
				{
					// default primary LTE
					strcpy(duster2UIpdp.rulename,"MT-LTE-Secondary");
				}
			}
			else
				strncpy(duster2UIpdp.rulename,pdp_context->PDP_name,sizeof(duster2UIpdp.rulename));
			/*rjin, 20140521, For LTE created PDP, if no PDP name using default name, end*/

			duster2UIpdp.cid = pdp_context->pdpinfo->PrimaryCID;
			duster2UIpdp.ip_type = pdp_context->pdpinfo->IP_Type;
			if(duster2UIpdp.cid == DEFAULE_CID_NUM && pdp_context->connection_num >= MIN_CONNUM_CREATBY_NETWORK) // only for default connection
			{
				memset(duster2UIpdp.apn,'\0',sizeof(duster2UIpdp.apn));
				strncpy(duster2UIpdp.apn,pdp_context->pdpinfo->APN,sizeof(duster2UIpdp.apn) - 1);
				//modify APN
				if(default_pdp_conf_modify(duster2UIpdp.apn, NULL) == 0)
					Duster_module_Printf(1,"APN is modified by CM new %s",duster2UIpdp.apn);
			}
			duster2UIpdp.connnum = pdp_context->connection_num;
			//duster2UIpdp.pconnnum = pdp_context->connection_num;   //need and primary connection number
			//memset(duster2UIpdp.rulename,'\0',64);
			//memcpy(duster2UIpdp.rulename,"default_pdp",11);
			duster2UIpdp.success = pdp_context->connect_status;
			Duster_module_Printf(1,"pdp_context->PDP_name : %s",duster2UIpdp.rulename);
			Duster_module_Printf(1,"%s: %s(%d) link status %d (0: disconn 1: connected 2 connecting)",__FUNCTION__,duster2UIpdp.rulename,duster2UIpdp.connnum,duster2UIpdp.success);
			Duster_module_Printf(1,"pdp_context->PrimaryCID : %d, pdp_context->IP_Type %d",duster2UIpdp.cid,duster2UIpdp.ip_type);
			if(pdp_context->pdpinfo != NULL)
			{
				duster2UIpdp.isdefault = pdp_context->pdpinfo->IsDefaultConnection;
				duster2UIpdp.secondary = pdp_context->pdpinfo->PDP_Type;
				strncpy(duster2UIpdp.apn,pdp_context->pdpinfo->APN,sizeof(duster2UIpdp.apn) - 1);
			}
			else
			{
				// set default APN
				strncpy(duster2UIpdp.apn,"cmnet",sizeof(duster2UIpdp.apn));
			}
			if(pdp_context->ip4info)
			{
				if(auto_apn == 1)
				{
					// in auto_apn, ONLY need check if the PDP is connected
					if(duster2UIpdp.success == 1 )
					{
						Duster_module_Printf(1,"%s: connction %d v4 link successfully",__FUNCTION__,duster2UIpdp.connnum);
						duster2UIpdp.ipv4 = ConvertIntegertoString(pdp_context->ip4info->IPAddr);
						if(web_dns_enable)
						{
							web_dns1 = psm_get_wrapper(PSM_MOD_LAN, NULL, "dns1");
							web_dns2 = psm_get_wrapper(PSM_MOD_LAN, NULL, "dns2");

							if(web_dns1)
								duster2UIpdp.v4dns1 = web_dns1;
							else
								duster2UIpdp.v4dns1 = duster_strdup("NA");

							if(web_dns2)
								duster2UIpdp.v4dns2 = web_dns2;
							else
								duster2UIpdp.v4dns2 = duster_strdup("NA");
						}
						else
						{
							duster2UIpdp.v4dns1= ConvertIntegertoString(pdp_context->ip4info->PrimaryDNS);
							duster2UIpdp.v4dns2 = ConvertIntegertoString(pdp_context->ip4info->SecondaryDNS);
						}
						duster2UIpdp.v4netmask= ConvertIntegertoString(pdp_context->ip4info->Mask);
						duster2UIpdp.v4gateway= ConvertIntegertoString(pdp_context->ip4info->GateWay);
						duster2UIpdp.curconntime = get_current_conn_second(connection_num,pdp_context->pdpinfo->connected_tick);
						Duster_module_Printf(1,"%s: current conn time %d",__FUNCTION__,duster2UIpdp.curconntime);
						duster2UIpdp.totalconntime = get_total_conn_second(connection_num,pdp_context->pdpinfo->total_connected_tick,pdp_context->pdpinfo->connected_tick);
						Duster_module_Printf(1,"%s: total conn time %d",__FUNCTION__,duster2UIpdp.totalconntime);
					}
					else
					{
						duster2UIpdp.ipv4 =  duster_strdup("NA");
						duster2UIpdp.v4dns1 = duster_strdup("NA");
						duster2UIpdp.v4dns2 = duster_strdup("NA");
						duster2UIpdp.v4netmask = duster_strdup("NA");
						duster2UIpdp.v4gateway = duster_strdup("NA");
					}
				}
				else
				{
					if(duster2UIpdp.success == 1 && (duster2UIpdp.ip_type == TYPE_V4V6 || duster2UIpdp.ip_type == TYPE_V4))
					{
						Duster_module_Printf(1,"%s: connction %d v4 link successfully",__FUNCTION__,duster2UIpdp.connnum);
						duster2UIpdp.ipv4 = ConvertIntegertoString(pdp_context->ip4info->IPAddr);

						if(web_dns_enable)
						{
							web_dns1 = psm_get_wrapper(PSM_MOD_LAN, NULL, "dns1");
							web_dns2 = psm_get_wrapper(PSM_MOD_LAN, NULL, "dns2");

							if(web_dns1)
								duster2UIpdp.v4dns1 = web_dns1;
							else
								duster2UIpdp.v4dns1 = duster_strdup("NA");

							if(web_dns2)
								duster2UIpdp.v4dns2 = web_dns2;
							else
								duster2UIpdp.v4dns2 = duster_strdup("NA");
						}
						else
						{
							duster2UIpdp.v4dns1= ConvertIntegertoString(pdp_context->ip4info->PrimaryDNS);
							duster2UIpdp.v4dns2 = ConvertIntegertoString(pdp_context->ip4info->SecondaryDNS);
						}
						duster2UIpdp.v4netmask= ConvertIntegertoString(pdp_context->ip4info->Mask);
						duster2UIpdp.v4gateway= ConvertIntegertoString(pdp_context->ip4info->GateWay);
						duster2UIpdp.curconntime = get_current_conn_second(connection_num,pdp_context->pdpinfo->connected_tick);
						Duster_module_Printf(1,"%s: current conn time %d",__FUNCTION__,duster2UIpdp.curconntime);
						duster2UIpdp.totalconntime = get_total_conn_second(connection_num,pdp_context->pdpinfo->total_connected_tick,pdp_context->pdpinfo->connected_tick);
						Duster_module_Printf(1,"%s: total conn time %d",__FUNCTION__,duster2UIpdp.totalconntime);
					}
					else
					{
						duster2UIpdp.ipv4 =  duster_strdup("NA");
						duster2UIpdp.v4dns1 = duster_strdup("NA");
						duster2UIpdp.v4dns2 = duster_strdup("NA");
						duster2UIpdp.v4netmask = duster_strdup("NA");
						duster2UIpdp.v4gateway = duster_strdup("NA");
					}
				}
			}
			else
			{
				if(duster2UIpdp.success == 1 && duster2UIpdp.secondary == 0)
				{
					duster2UIpdp.curconntime = get_current_conn_second(connection_num,pdp_context->pdpinfo->connected_tick);
					Duster_module_Printf(1,"%s: second PDP current conn time %d",__FUNCTION__,duster2UIpdp.curconntime);
					duster2UIpdp.totalconntime = get_total_conn_second(connection_num,pdp_context->pdpinfo->total_connected_tick,pdp_context->pdpinfo->connected_tick);
					Duster_module_Printf(1,"%s: second PDP total conn time %d",__FUNCTION__,duster2UIpdp.totalconntime);
				}
				duster2UIpdp.ipv4 =  duster_strdup("NA");
				duster2UIpdp.v4dns1 = duster_strdup("NA");
				duster2UIpdp.v4dns2 = duster_strdup("NA");
				duster2UIpdp.v4netmask = duster_strdup("NA");
				duster2UIpdp.v4gateway = duster_strdup("NA");
			}
			if(pdp_context->ip6info)
			{
				if(auto_apn == 1)
				{
					if(duster2UIpdp.success == 1)
					{
						Duster_module_Printf(1,"%s: connection %d v6 link successfully",__FUNCTION__,duster2UIpdp.connnum);
						#ifndef NO_LWIP_NETIF
						netif_get_default_nw_ip6addr(&ipv6_address);
						#endif
						if(ipv6_address.local_ip6[0] ==0 && ipv6_address.local_ip6[1] ==0 && ipv6_address.local_ip6[2] ==0 && ipv6_address.local_ip6[3] ==0)
							duster2UIpdp.ipv6 =  duster_strdup("NA");
						else
							duster2UIpdp.ipv6 =	ConvertV6IntegertoString((INT32 *)ipv6_address.local_ip6);
						if(ipv6_address.global_ip6[0] ==0 && ipv6_address.global_ip6[1] ==0 && ipv6_address.global_ip6[2] ==0 && ipv6_address.global_ip6[3] ==0)
							duster2UIpdp.g_ipv6 =  duster_strdup("NA");
						else
							duster2UIpdp.g_ipv6 =	ConvertV6IntegertoString((INT32 *)ipv6_address.global_ip6);
						duster2UIpdp.v6dns1 = ConvertV6IntegertoString((INT32 *)(pdp_context->ip6info->PrimaryDNS));
						duster2UIpdp.v6dns2 = ConvertV6IntegertoString((INT32 *)(pdp_context->ip6info->SecondaryDNS));
						duster2UIpdp.v6netmask = ConvertV6IntegertoString((INT32 *)(pdp_context->ip6info->Mask));
						duster2UIpdp.v6gateway= ConvertV6IntegertoString((INT32 *)(ipv6_address.gateway_ip6));

						duster2UIpdp.curconntime = get_current_conn_second(connection_num,pdp_context->pdpinfo->connected_tick);
						Duster_module_Printf(1,"%s: current conn time %d",__FUNCTION__,duster2UIpdp.curconntime);
						duster2UIpdp.totalconntime = get_total_conn_second(connection_num,pdp_context->pdpinfo->total_connected_tick,pdp_context->pdpinfo->connected_tick);
						Duster_module_Printf(1,"%s: total conn time %d",__FUNCTION__,duster2UIpdp.totalconntime);
					}
					else
					{
						duster2UIpdp.ipv6 =  duster_strdup("NA");
						duster2UIpdp.g_ipv6 =  duster_strdup("NA");
						duster2UIpdp.v6dns1 =  duster_strdup("NA");
						duster2UIpdp.v6dns2 =  duster_strdup("NA");
						duster2UIpdp.v6netmask =  duster_strdup("NA");
						duster2UIpdp.v6gateway =  duster_strdup("NA");
					}
				}
				else
				{
					if(duster2UIpdp.success == 1 &&(duster2UIpdp.ip_type == TYPE_V4V6 || duster2UIpdp.ip_type == TYPE_V6))
					{
						Duster_module_Printf(1,"%s: connection %d v6 link successfully",__FUNCTION__,duster2UIpdp.connnum);
						#ifndef NO_LWIP_NETIF
						netif_get_default_nw_ip6addr(&ipv6_address);
						#endif
						if(ipv6_address.local_ip6[0] ==0 && ipv6_address.local_ip6[1] ==0 && ipv6_address.local_ip6[2] ==0 && ipv6_address.local_ip6[3] ==0)
							duster2UIpdp.ipv6 =  duster_strdup("NA");
						else
							duster2UIpdp.ipv6 =	ConvertV6IntegertoString((INT32 *)ipv6_address.local_ip6);
						if(ipv6_address.global_ip6[0] ==0 && ipv6_address.global_ip6[1] ==0 && ipv6_address.global_ip6[2] ==0 && ipv6_address.global_ip6[3] ==0)
							duster2UIpdp.g_ipv6 =  duster_strdup("NA");
						else
							duster2UIpdp.g_ipv6 =	ConvertV6IntegertoString((INT32 *)ipv6_address.global_ip6);
						duster2UIpdp.v6dns1 = ConvertV6IntegertoString((INT32 *)(pdp_context->ip6info->PrimaryDNS));
						duster2UIpdp.v6dns2 = ConvertV6IntegertoString((INT32 *)(pdp_context->ip6info->SecondaryDNS));
						duster2UIpdp.v6netmask = ConvertV6IntegertoString((INT32 *)(pdp_context->ip6info->Mask));
						duster2UIpdp.v6gateway= ConvertV6IntegertoString((INT32 *)ipv6_address.gateway_ip6);

						duster2UIpdp.curconntime = get_current_conn_second(connection_num,pdp_context->pdpinfo->connected_tick);
						Duster_module_Printf(1,"%s: current conn time %d",__FUNCTION__,duster2UIpdp.curconntime);
						duster2UIpdp.totalconntime = get_total_conn_second(connection_num,pdp_context->pdpinfo->total_connected_tick,pdp_context->pdpinfo->connected_tick);
						Duster_module_Printf(1,"%s: total conn time %d",__FUNCTION__,duster2UIpdp.totalconntime);
					}
					else
					{
						duster2UIpdp.ipv6 =  duster_strdup("NA");
						duster2UIpdp.g_ipv6 =  duster_strdup("NA");
						duster2UIpdp.v6dns1 =  duster_strdup("NA");
						duster2UIpdp.v6dns2 =  duster_strdup("NA");
						duster2UIpdp.v6netmask =  duster_strdup("NA");
						duster2UIpdp.v6gateway =  duster_strdup("NA");
					}
				}
			}
			else
			{
				if(duster2UIpdp.success == 1 && duster2UIpdp.secondary == 0)
				{
					duster2UIpdp.curconntime = get_current_conn_second(connection_num,pdp_context->pdpinfo->connected_tick);
					Duster_module_Printf(1,"%s: second PDP current conn time %d",__FUNCTION__,duster2UIpdp.curconntime);
					duster2UIpdp.totalconntime = get_total_conn_second(connection_num,pdp_context->pdpinfo->total_connected_tick,pdp_context->pdpinfo->connected_tick);
					Duster_module_Printf(1,"%s: second PDP total conn time %d",__FUNCTION__,duster2UIpdp.totalconntime);
				}
				duster2UIpdp.ipv6 =  duster_strdup("NA");
				duster2UIpdp.g_ipv6 =  duster_strdup("NA");
				duster2UIpdp.v6dns1 =  duster_strdup("NA");
				duster2UIpdp.v6dns2 =  duster_strdup("NA");
				duster2UIpdp.v6netmask =  duster_strdup("NA");
				duster2UIpdp.v6gateway =  duster_strdup("NA");
			}
			snprintf(tmp_buf,ONE_PDP_LENGTH,"%s%c%d%c%d%c%d%c%d%c%d%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%u%c%u%c",\
			         duster2UIpdp.rulename,fd,duster2UIpdp.connnum,fd,duster2UIpdp.pconnnum,fd,\
			         duster2UIpdp.success,fd,duster2UIpdp.isdefault,fd,duster2UIpdp.secondary,fd,\
			         duster2UIpdp.ipv4,fd,duster2UIpdp.v4dns1,fd,duster2UIpdp.v4dns2,fd,\
			         duster2UIpdp.v4gateway,fd,duster2UIpdp.v4netmask,fd,duster2UIpdp.ipv6,fd,\
			         duster2UIpdp.g_ipv6,fd,duster2UIpdp.v6dns1,fd,duster2UIpdp.v6dns2,fd,duster2UIpdp.v6gateway,fd,\
			         duster2UIpdp.v6netmask,fd,duster2UIpdp.curconntime,fd,duster2UIpdp.totalconntime,rd);
			Duster_module_Printf(1,"pdp_context:%s",tmp_buf);
			strcat(pdp_context_buf,tmp_buf);
			free_link_status(pdp_context);
			free_v4info(&duster2UIpdp);
			free_v6info(&duster2UIpdp);
		}
		#if defined(CRANE_WEBUI_SUPPORT)
		if(isLTENetwork(0))
		#else
		if(isLTENetwork())
		#endif
		{
			if(connection_num == MAX_MO_PDP_NUMBER)
				connection_num = MIN_CONNUM_CREATBY_NETWORK-1;

		}
	}
	psm_set_wrapper(PSM_MOD_WAN, "cellular", "pdp_context_list",pdp_context_buf);

	Duster_module_Printf(1,"leave %s", __FUNCTION__);
}

int get_default_mtu(int *res_mtu)
{
#define MCC_MNC_MAX_LEN 16
#define TMP_BUF_SIZE 512
	char mcc_mnc[MCC_MNC_MAX_LEN] = {'\0'};
	char file_name[64] = {'\0'};
	FILE_ID fid = 0;
	char *tmp_buf = NULL;
	int read_size = 0;
	char *item_begin = NULL;
	char *item_end = NULL;
	char temp[16] = {'\0'};
	char mcc_mnc_tmp[MCC_MNC_MAX_LEN] = {'\0'};
	int  mtu;
	int try_num = 0;

	while(strlen(IMSI) < 7 && try_num < 5)
	{
		try_num++;
		OSATaskSleep(50);
	}
	if(try_num == 5)
	{
		Duster_module_Printf(1,"%s IMSI not ready",__FUNCTION__);
		goto END;
	}
	Duster_module_Printf(1,"%s IMSI ready %d",__FUNCTION__,try_num);
	memcpy(mcc_mnc,IMSI, 3+gMncLen);
	mcc_mnc[3+gMncLen] = '\0';

	Duster_module_Printf(1,"%s mcc_mnc %s",__FUNCTION__,mcc_mnc);
	sprintf(file_name, "www\\data\\mtu.txt");
	Duster_module_Printf(1,"%s file_name is %s",__FUNCTION__,	file_name);
	fid = FDI_fopen(file_name, "rb");
	if (fid == NULL)
	{
		Duster_module_Printf(1, "%s Couldn't read file", __FUNCTION__);
		goto END;
	}
	tmp_buf = duster_malloc(TMP_BUF_SIZE);
	if(tmp_buf == NULL)
	{
		Duster_module_Printf(1, "%s Couldn't read file", __FUNCTION__);
		goto END;
	}
	read_size = FDI_fread(tmp_buf,	1,	(size_t)TMP_BUF_SIZE,	fid);
	if(read_size <= 0)
	{
		Duster_module_Printf(1, "%s read file size %d", __FUNCTION__,read_size);
		goto END;
	}
	if(fid > 0)
	{
		FDI_fclose(fid);
		fid = 0;
	}
	if(read_size>= TMP_BUF_SIZE)
	{

		Duster_module_Printf(1, "%s read file size %d too large", __FUNCTION__,read_size);
		goto END;
	}
	Duster_module_Printf(1,"%s read size %d",__FUNCTION__,	read_size);
	tmp_buf[read_size] = '\0';
	item_begin = strchr(tmp_buf, '{');
	while(item_begin != NULL)
	{
		memset(temp, '\0', 16);
		item_end = strchr(item_begin, '}');

		if (item_end == NULL || item_end - item_begin >= sizeof(temp))
			break;

		item_begin++;
		memcpy(temp, item_begin, item_end - item_begin);
		item_end++;
		item_begin = strchr(item_end, '{');

		CPUartLogPrintf("%s tmpbuf is %s",	__FUNCTION__, temp);

		if (strchr(temp, ',') == NULL)
			break;

		if (sscanf(temp, "%[^,], %d", mcc_mnc_tmp,&mtu) != 2)
		{
			CPUartLogPrintf("%s sscanf failed",	__FUNCTION__);
			continue;
		}

		CPUartLogPrintf("%s: parsed mccmnc %s mtu %d",__FUNCTION__,mcc_mnc_tmp,mtu);
		if (!strncmp(mcc_mnc, mcc_mnc_tmp,strlen(mcc_mnc_tmp)))
		{
			if(tmp_buf)
			{
				duster_free(tmp_buf);
				tmp_buf = NULL;
			}
			*res_mtu = mtu;
			return 0;
		}
		else
		{
			CPUartLogPrintf("%s: not find mccmnc %s in loop",__FUNCTION__, mcc_mnc);
			continue;
		}

	}

END:
	if(tmp_buf)
	{
		duster_free(tmp_buf);
		tmp_buf = NULL;
	}
	if(fid > 0)
	{
		FDI_fclose(fid);
		fid = 0;
	}
	mtu = 1500;
	*res_mtu = mtu;
	return -1;;
}

/*this APN is set from ps_api.c*/
#include "ps_api.h"
telPsDefaultPdpApn *telGetDefaultPdpApn(void);

void wan_get_auto_pdp_list(void)
{
	int list_num = 0;
	char *str = NULL;
	int auto_apn = 0;
	int NW_status = 0;
	char fd,rd;
	Apn_Info *match_apninfo = NULL;
	char tmp_buf[512] = {0};
	char username[32] = {'\0'};
	char password[32] = {'\0'};
	telPsDefaultPdpApn *default_pdp_apn = NULL;
	char *apn_name = NULL;

	duster_call_module_get_delimiters(PSM_MOD_WAN, &fd, &rd);

	auto_apn = getAutoApn();
	Duster_module_Printf(1,"%s: auto_apn %d",__FUNCTION__,auto_apn);
	if((gSimStatus == CI_SIM_ST_READY) && (IMSI_APN == NULL))
	{
		if(!IMSI_APN)
		{
		    #if defined(CRANE_WEBUI_SUPPORT)
			IMSI_APN = wan_get_apn((CMSimID)0);
			#else
			IMSI_APN = wan_get_apn();
			#endif
		}

	}
	if(PlatformGetBoardType() == NEZHA_MMIFI_V3_BOARD)
	{
		if(IMSI_APN)
		{
			IMSI_APN->num = 0;
		}
	}
	if(IMSI_APN == NULL )
	{
		Duster_module_Printf(1,"IMSI_APN is NULL ",__FUNCTION__);
		sprintf(tmp_buf,"%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c"," ",fd," ",fd,\

		        " ",fd," ",fd," ",fd," ",fd," ",fd," ",fd," ",fd," ",fd, " ",fd," ",fd," ",rd);
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "pdp_auto_list",tmp_buf);
		return;
	}
	if(IMSI_APN->num == 0)
	{
		default_pdp_apn = telGetDefaultPdpApn();
		if (default_pdp_apn)
		{
			apn_name = malloc(strlen(default_pdp_apn->epsApn) + 1);
			strcpy(apn_name, default_pdp_apn->epsApn);
			psm_set_wrapper(PSM_MOD_WAN,	"cellular",	"nw_apn",	apn_name);
		}
		else
		{
			apn_name = strdup(" ");
			psm_set_wrapper(PSM_MOD_WAN,	"cellular",	"nw_apn",	"");
		}

		if(IMSI_APN->apn_info_list)
			sprintf(tmp_buf,"%s%c%s%c%s%c%s%c%s%c%d%c%s%c%s%c%s%c%s%c%s%c%s%c%d%c",IMSI_APN->mcc,fd,IMSI_APN->mnc,fd,\
			        IMSI_APN->apn_info_list->oper_name,fd," ",fd,apn_name,fd, "2",fd," ",fd," ",fd," ",fd,\
			        " ",fd," ",fd," ",fd,IMSI_APN->apn_info_list->iptype,rd);
		else
			sprintf(tmp_buf,"%s%c%s%c%s%c%s%c%s%c%d%c%s%c%s%c%s%c%s%c%s%c%s%c%d%c",IMSI_APN->mcc,fd,IMSI_APN->mnc,fd,\
			        " ",fd," ",fd,apn_name,fd, "2",fd," ",fd," ",fd," ",fd,\
			        " ",fd," ",fd," ",fd,"0",rd);
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "pdp_auto_list",tmp_buf);

		if (apn_name)
			free(apn_name);

		return;

	}
	/*add NW status and auto apn settings first*/
	NW_status = dialer_Check_NW(AT_WEBUI_CHANNEL);/*get NW status*/
	if(NW_status < 0)/*Get NW status failed try second time*/
	{
		NW_status = dialer_Check_NW(AT_WEBUI_CHANNEL);/*get NW status*/
	}

	//@20150128, no matter auto-apn configuration, always get auto apn info list
	#if defined(CRANE_WEBUI_SUPPORT)
	match_apninfo = wan_get_right_apn(NW_status,NULL);
	#else
	match_apninfo = wan_get_right_apn(NW_status);
	#endif
	if(match_apninfo != NULL)
	{
		Duster_module_Printf(1,"%s: mcc %s",__FUNCTION__,IMSI_APN->mcc);
		Duster_module_Printf(1,"%s: mnc %s",__FUNCTION__,IMSI_APN->mnc);
		if(match_apninfo->oper_name)
		{
			Duster_module_Printf(1,"%s: oper_name %s",__FUNCTION__,match_apninfo->oper_name);
		}
		else
		{
			Duster_module_Printf(1,"%s: oper_name",__FUNCTION__);
		}
		Duster_module_Printf(1,"%s: nwtype %d",__FUNCTION__,match_apninfo->nwtype);
		// mcc,mnc,oper_name,apn,lte_apn,network_type, 2g3gauth_type,2g3guser_name,2g3gpassword,4gauth_type,4guser_name,4gpassword,iptype

		if(match_apninfo->nwtype == 0)// none
		{
			Duster_module_Printf(1,"%s: apn %s",__FUNCTION__,match_apninfo->apn);
			// set IPTYPE to IPV4
			sprintf(tmp_buf,"%s%c%s%c%s%c%s%c%s%c%d%c%s%c%s%c%s%c%s%c%s%c%s%c%d%c",IMSI_APN->mcc,fd,IMSI_APN->mnc,fd,\

			        match_apninfo->oper_name,fd,match_apninfo->apn,fd,match_apninfo->apn,fd, match_apninfo->nwtype,fd,"NONE",fd," ",fd," ",fd,\
			        "NONE",fd," ",fd," ",fd,match_apninfo->iptype,rd);

		}
		else if(match_apninfo->nwtype == 2)// lte
		{
			Duster_module_Printf(1,"%s: lte_apn %s",__FUNCTION__,match_apninfo->apn);
			Duster_module_Printf(1,"%s: authtype %s",__FUNCTION__,match_apninfo->authtype);
			if(match_apninfo->usrname)
			{
				Duster_module_Printf(1,"%s: username %s",__FUNCTION__,match_apninfo->usrname);
				strncpy(username, match_apninfo->usrname,31);
			}
			else
			{
				strcpy(username, "any");
			}
			if(match_apninfo->paswd)
			{
				Duster_module_Printf(1,"%s: paswd %s",__FUNCTION__,match_apninfo->paswd);
				strncpy(password, match_apninfo->paswd, 31);
			}
			else
			{
				strcpy(password, "any");
			}
			Duster_module_Printf(1,"%s: iptype %d",__FUNCTION__,match_apninfo->iptype);

			sprintf(tmp_buf,"%s%c%s%c%s%c%s%c%s%c%d%c%s%c%s%c%s%c%s%c%s%c%s%c%d%c",IMSI_APN->mcc,fd,IMSI_APN->mnc,fd,\

			        match_apninfo->oper_name,fd," ",fd,match_apninfo->apn,fd, match_apninfo->nwtype,fd," ",fd," ",fd," ",fd,\
			        match_apninfo->authtype,fd,username,fd,password,fd,match_apninfo->iptype,rd);
		}
		else
		{
			Duster_module_Printf(1,"%s: apn %s",__FUNCTION__,match_apninfo->apn);
			Duster_module_Printf(1,"%s: authtype %s",__FUNCTION__,match_apninfo->authtype);
			if(match_apninfo->usrname)
			{
				Duster_module_Printf(1,"%s: username %s",__FUNCTION__,match_apninfo->usrname);
				strncpy(username, match_apninfo->usrname,31);
			}
			else
			{
				strcpy(username, "any");
			}
			if(match_apninfo->paswd)
			{
				Duster_module_Printf(1,"%s: paswd %s",__FUNCTION__,match_apninfo->paswd);
				strncpy(password, match_apninfo->paswd, 31);
			}
			else
			{
				strcpy(password, "any");
			}
			Duster_module_Printf(1,"%s: iptype %d",__FUNCTION__,match_apninfo->iptype);
			sprintf(tmp_buf,"%s%c%s%c%s%c%s%c%s%c%d%c%s%c%s%c%s%c%s%c%s%c%s%c%d%c",IMSI_APN->mcc,fd,IMSI_APN->mnc,fd,\

			        match_apninfo->oper_name,fd,match_apninfo->apn,fd," ",fd,match_apninfo->nwtype,fd,\
			        match_apninfo->authtype,fd,username,fd,password,fd," ",fd, " ",fd," ",fd,match_apninfo->iptype,rd);
		}
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "pdp_auto_list",tmp_buf);
	}
	else
	{
		sprintf(tmp_buf,"%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c"," ",fd," ",fd,\

		        " ",fd," ",fd," ",fd," ",fd," ",fd," ",fd," ",fd," ",fd, " ",fd," ",fd," ",rd);
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "pdp_auto_list",tmp_buf);

	}
	return;
}

int wan_pre_get(int config_action, dc_args_t *callback_args)
{
	Duster_module_Printf(1,"enter %s",__FUNCTION__);
	MrvXMLElement *root;

	if ( !callback_args )
		return -1;

	if ( callback_args->dc_type != DUSTER_CB_ARGS_XML )
		return -1;

	root = callback_args->dc_root;
	if ( !root )
		return -1;

	wan_get_pdp_context();
	wan_get_auto_pdp_list();

	Duster_module_Printf(1,"leave %s",__FUNCTION__);
	return 0;
}

int wan_get(int config_action, dc_args_t *callback_args)
{
	//openlog("duster:wan", LOG_ODELAY, LOG_SYSLOG);
	Duster_module_Printf(1,"enter %s",__FUNCTION__);
	//mxml_node_t *root;
	MrvXMLElement *root = NULL;

	if ( !callback_args )
		return -1;

	if ( callback_args->dc_type != DUSTER_CB_ARGS_XML )
		return -1;

	//root = callback_args->dc_data.dc_subnode;
	root = callback_args->dc_root;
	if ( !root )
		return -1;
	wan_xml_get(root);
	setAPPinfo(root);
	//wan_get_pdp_context();


	Duster_module_Printf(1,"leave %s",__FUNCTION__);
	return 0;
}


int wan_pre_set(int Action, dc_args_t *dca )
{
	char *str=NULL;

	Duster_module_Printf(1,"enter %s: Action is %d",__FUNCTION__,Action);

	if(Action == DUSTER_CONFIG_START)  //start
		return 0;

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "proto");
	if(str)
	{

		if(strlen(str) < 10)
		{
			memset(gUI2DialerPara.proto, 0 , 10);
			memcpy(gUI2DialerPara.proto, str, strlen(str));
		}
		else
			duster_Printf("%s: length of gUI2DialerPara.proto is too long", __FUNCTION__);

		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "connect_disconnect");
	if(str)
	{

		if(strlen(str) < 10)
		{
			memset(gUI2DialerPara.connect_disconnect, 0 , 10);
			memcpy(gUI2DialerPara.connect_disconnect, str, strlen(str));
		}
		else
			duster_Printf("%s: length of gUI2DialerPara.connect_disconnect is too long", __FUNCTION__);

		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "connect_mode");
	if(str)
	{
		Duster_module_Printf(1,"%s: pre connectmode %s",__FUNCTION__,str);
		gUI2DialerPara.connectmode = (str[0]-'0');
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "Engineering_mode");
	if(str != NULL)
	{
		EngiMode = atoi(str);
		Duster_module_Printf(1, "%s: EngiMode is %d", __FUNCTION__,EngiMode);
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "version_flag");
	if(str != NULL)
	{
		if((atoi(str) > 1) && switch_ctx.auto_switch)
		{
			set_auto_switch(FALSE, TRUE);
			Duster_module_Printf(1,"%s: disable auto_switch",__FUNCTION__);
		}
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "prefer_lte_type");
	if(str != NULL)
	{
        PreferLteType = atoi(str);
		Duster_module_Printf(1,"%s: prefer_lte_type is %d",__FUNCTION__,PreferLteType);
		duster_free(str);
		str = NULL;
    }

	Duster_module_Printf(1,"leave %s",__FUNCTION__);
    return 0;
}

void wan_set_netif_cm_tracked_mtu(int mtu)
{
	int i;
	for (i = 0; i < 15; i++)
	{
		if (CM_IsCidTracked(i))
			lwip_set_netif_pdp_mtu(mtu, i);
	}

	return;
}

void wan_set_mtu_lwip(int Action)
{
	char *str = NULL;
	char *str1, *str2 =NULL;
	int set_mtu = 0;
	char mtu_tmp[6] = {'\0'};
	static int set_done = 0;
	
#ifndef NO_LWIP_NETIF
	if(Action == DUSTER_CONFIG_START)
	{
		psm_set_wrapper(PSM_MOD_WAN, NULL, "mtu_action","0");
	}

	if (getAutoApn())
		return;
	
	str2 = psm_get_wrapper(PSM_MOD_WAN, NULL, "mtu_action");
	str1 = psm_get_wrapper(PSM_MOD_WAN, NULL, "mtu");
	if(!strcmp(str2,"1"))
	{
		// change by Web-UI
		psm_set_wrapper(PSM_MOD_WAN, NULL, "mtu_action","0");
		psm_set_wrapper(PSM_MOD_WAN, NULL, "mtu_set_flag","1");// modify by Web-UI
		if(str1 != NULL)
		{
			Duster_module_Printf(1, "%s: set mtu %d", __FUNCTION__, atoi(str1));
			wan_set_netif_cm_tracked_mtu(atoi(str1));
			//if(IsMbimEnabled() == 1)
	        //    MbimRilIPMTUChangeInd(atoi(str1));
			duster_free(str1);
			str1= NULL;
		}
	}
	else
	{
		int file_mtu;
		int mtu_location;
		str = psm_get_wrapper(PSM_MOD_WAN, NULL, "mtu_set_flag");
		if(str == NULL || !strncmp(str,"",1))
		{
			// update default MTU
			if(set_done == 0)
			{
				mtu_location = get_default_mtu(&file_mtu);
				Duster_module_Printf(1, "%s: set default MTU %d", __FUNCTION__, file_mtu);
				if(mtu_location == 0)
				{
					snprintf(mtu_tmp,6,"%d",file_mtu);
					psm_set_wrapper(PSM_MOD_WAN, NULL, "mtu", mtu_tmp);
					wan_set_netif_cm_tracked_mtu(file_mtu);
					//if(IsMbimEnabled() == 1)
					//    MbimRilIPMTUChangeInd(file_mtu);
				}
				else
				{
					if(str1 && strncmp(str1,"",1))
					{
						Duster_module_Printf(1, "%s: set default MTU[psm] %d", __FUNCTION__,atoi(str1));
						wan_set_netif_cm_tracked_mtu(atoi(str1));
						//if(IsMbimEnabled() == 1)
					    //    MbimRilIPMTUChangeInd(atoi(str1));
					}
					else
					{
						Duster_module_Printf(1, "%s: MTU NOT in MTU.txt and PSM", __FUNCTION__);
						memset(mtu_tmp,'\0',6);
						snprintf(mtu_tmp,6,"%d",1500);
						psm_set_wrapper(PSM_MOD_WAN, NULL, "mtu", mtu_tmp);
						wan_set_netif_cm_tracked_mtu(1500);// in this case, 1500 as default
						//if(IsMbimEnabled() == 1)
					    //    MbimRilIPMTUChangeInd(1500);
					}
				}
				set_done  = 1;
			}
			else
			{
				Duster_module_Printf(1, "%s: MTU not change", __FUNCTION__);
			}
		}
		else if(str && !strcmp(str,"1"))
		{
			// In this case, the MTU has been set by Web-UI before system reset
			if(str1 && strncmp(str1,"",1))
			{
				Duster_module_Printf(1, "%s: set default MTU[Web-UI] %d", __FUNCTION__,atoi(str1));
				wan_set_netif_cm_tracked_mtu(atoi(str1));
				//if(IsMbimEnabled() == 1)
			    //    MbimRilIPMTUChangeInd(atoi(str1));
			}
			else
			{
				Duster_module_Printf(1, "%s: MTU NOT in PSM", __FUNCTION__);
				memset(mtu_tmp,'\0',6);
				snprintf(mtu_tmp,6,"%d",1500);
				psm_set_wrapper(PSM_MOD_WAN, NULL, "mtu", mtu_tmp);
				wan_set_netif_cm_tracked_mtu(1500);// in this case, 1500 as default
                //if(IsMbimEnabled() == 1)
			    //    MbimRilIPMTUChangeInd(1500);
			}
		}
		else
		{
			// should not be in this branch
		}
	}
	if(str)
	{
		duster_free(str);
		str = NULL;
	}
	if(str1 != NULL)
	{
		duster_free(str1);
		str1 = NULL;
	}
	if(str2 != NULL)
	{
		duster_free(str2);
		str2 = NULL;
	}
#endif
}
#if !defined(CRANE_WEBUI_SUPPORT)
char get_network_mode(void)
{
	char *str = NULL;
	char mode;
	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "NW_mode");
	if (str)
	{
		switch(atoi(str))
		{
			case 1:           //triple mode
				NWmode = CI_DEV_NW_TRIP_MODE_LTE;
				break;
			case 2:           //4LTE only
				NWmode = CI_DEV_NW_LTE_MODE;
				psm_set_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode","0");
				break;
			case 3:           //4G/3G mode
				NWmode = CI_DEV_NW_DUAL_UMTS_LTE_MODE_LTE;
				break;
			case 4:           //3G/2G mode
				NWmode = CI_DEV_NW_DUAL_GSM_UMTS_MODE_UMTS;
				break;
			case 5:           //3G only
				NWmode = CI_DEV_NW_UMTS_MODE;
				psm_set_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode","0");
				break;
			case 6:           //2G only
				NWmode = CI_DEV_NW_GSM_MODE;
				psm_set_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode","0");
				break;
			case 8:
				NWmode = CI_DEV_NW_DUAL_GSM_LTE_MODE;
				break;
#ifdef QUECTEL_PROJECT_CUST
			case 9:			//Auto
				NWmode = CI_DEV_NW_LTE_NR_MODE_NR;
				break;
			case 10:		//5G only
				NWmode = CI_DEV_NW_NR_MODE;
				break;
			case 11:		//LTE only
				NWmode = CI_DEV_NW_LTE_MODE;
				break;
#endif
			default:
				Duster_module_Printf(1,"%s: inputed NW_mode is not supported",__FUNCTION__);
				break;
		}
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode");
	if(str != NULL)
	{
		switch(atoi(str))
		{
			case 1:           //4G prefer(Triple mode)
				NWmode = CI_DEV_NW_TRIP_MODE_LTE;
				break;

			case 2:           //3G prefer(Triple mode)
				NWmode = CI_DEV_NW_TRIP_MODE_UMTS;
				break;

			case 3:           //4G prefer (3G/4G)
				NWmode = CI_DEV_NW_DUAL_UMTS_LTE_MODE_LTE;
				break;

			case 4:           //3G prefer (3G/4G)
				NWmode = CI_DEV_NW_DUAL_UMTS_LTE_MODE_UMTS;
				break;

			case 5:           //3G prefer (2G/3G)
				NWmode = CI_DEV_NW_DUAL_GSM_UMTS_MODE_UMTS;
				break;

			case 6:           //2G prefer (2G/3G)
				NWmode = CI_DEV_NW_DUAL_GSM_UMTS_MODE_GSM;
				break;

			case 7:           //2G prefer(Triple mode)           //need webUI add this item
				NWmode = CI_DEV_NW_TRIP_MODE_GSM;
				break;

			case 8:
				NWmode = CI_DEV_NW_DUAL_GSM_LTE_MODE_GSM;
				break;

			case 9:
				NWmode = CI_DEV_NW_DUAL_GSM_LTE_MODE_LTE;
				break;

			default:
				Duster_module_Printf(1,"%s: inputed prefer_mode is not supported",__FUNCTION__);
				break;
		}
		duster_free(str);
	}
	return NWmode;
}
#endif
void wan_post_set_cm(int Action,UINT32 at_channel)
{
	char *str1, *str= NULL;
	char *str2 = NULL;

	Duster_module_Printf(1,"enter: %s, at_channel %d",__FUNCTION__, at_channel);
	if(Action == DUSTER_CONFIG_START)
	{
		if(stat_reach_limit())
		{
			wan_cellular_stop_cm();
			return;
		}
	}
	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "proto");

	if(str)
		Duster_module_Printf(1, "%s: proto %s", __FUNCTION__,str);

	if(strcmp(str, "cellular") == 0)
	{
		str2 = psm_get_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_dial");
		if(str2 != NULL)
		{
		    #if defined(CRANE_WEBUI_SUPPORT)
		    if(!strncmp(str2,"1", 1) && (CIMM_REGSTATUS_ROAMING == get_current_reg_status(0)))
		    #else
			if(!strncmp(str2,"1", 1) && (CIMM_REGSTATUS_ROAMING == get_current_reg_status()))
			#endif
			{
				// should not trigger dial any more in roaming
				duster_free(str2);
				str2 = NULL;
				wan_cellular_stop_cm();
				Duster_module_Printf(1, "%s: disable data connection in roaming", __FUNCTION__);
				if(str != NULL) duster_free(str);
				return;
			}
			duster_free(str2);
			str2 = NULL;
		}
		if((strcmp(gUI2DialerPara.proto,"disabled") == 0)||((Action == DUSTER_CONFIG_START)) || (ManualSelectNW == TRUE ))
		{
			Duster_module_Printf(1, "%s: 1", __FUNCTION__);
			ManualSelectNW = FALSE;
			wan_cellular_start_cm(Action, at_channel);
		}
		else
		{
			Duster_module_Printf(1, "%s: 2", __FUNCTION__);

			//str1 = psm_get_wrapper(PSM_MOD_WAN, NULL, "connect_disconnect");
			str1 = psm_get_wrapper(PSM_MOD_WAN, NULL, "auto_apn_action");
			Duster_module_Printf(1, "%s: auto_apn_action is %s %d", __FUNCTION__,str1,atoi(str1));


			if(atoi(str1) != 1)
			{
				ManualSelectNW = FALSE;
				wan_cellular_start_cm(Action, at_channel);
			}
			else
			{
				CM_clear_first_dial_flag();
				ManualSelectNW = FALSE;
				wan_cellular_stop_cm();
				if(IMSI_APN)
				{
					IMSI_APN->try_num_ESM_false = 0;
				}
				wan_cellular_start_cm(Action, at_channel);
				//do not clear flag in this pointer rjin 20140221s
				//psm_set_wrapper(PSM_MOD_WAN, NULL, "auto_apn_action","0");
				Duster_module_Printf(1, "%s: clear auto_apn_action", __FUNCTION__);
			}


			if(str1)
			{
				duster_free(str1);
				str1 = NULL;
			}
		}
	}
	else if((strcmp(str, "disabled") == 0) && (strcmp(gUI2DialerPara.proto,"cellular") == 0))
		wan_cellular_stop_cm();

	if(str)
	{
		duster_free(str);
		str = NULL;
	}
	Duster_module_Printf(1,"leave: %s",__FUNCTION__);
}

int wan_post_set(int Action, dc_args_t *dca )
{
	char *str = NULL,*str1=NULL,*str2=NULL, *str3=NULL, *str4=NULL;
	char *MsgBuf=NULL;
	char at_str[DIALER_MSG_SIZE] = {'\0'};
	char resp_str[DIALER_MSG_SIZE] = {'\0'};
	int ret, i,BG_ScanTime=0, enableflag=0;
	char *p1=NULL, *p2=NULL;
	int enable_bgscan = 0;
	char PLMN[8]= {'\0'};
	char Act[2]= {'\0'};
	char bg_time[2]= {0};
	OSA_STATUS osaStatus;
	UINT32 bandPriorityFlag = 0;
	BOOL disable_NW = FALSE;
	BOOL config_NW = FALSE;
	WlanClientStatusMessage  status_msg;
	int roaming_status = 0, RetryNum=0;
	BOOL psNVMSetting = FALSE;
	UINT32 at_channel  = 0;
	UINT32 WebUINWmode=0,WebUIPreferMode=0;
	char GsmBand[4]= "0";
	char UmtsBand[6]= "0";
	char LTEBandL[10]= "0";
	char LTEBandH[10]= "0";
	char roamingConfig[4]= "0";
	char srvDomain[4]= "0";
	char bandPriorityFlag_tmp[4]= "0";
	char LteBandExt[12]= "0";

	UINT32 G_band=0, U_band=0, L_bandl=0, L_bandh=0,ltebandext=0;
#ifdef QUECTEL_PROJECT_CUST
	BOOL is_prefer_lte_type = FALSE;
#endif

	Duster_module_Printf(1,"enter %s: Action is %d",__FUNCTION__,Action);

	wanAction = Action;

	while((ATCmdSvrRdy == FALSE) && (RetryNum < 60))
	{
		OSATaskSleep(1000);       //delay 5s
		Duster_module_Printf(1,"%s: waiting for atcmdsrv ready",__FUNCTION__);
		RetryNum++;
	}

	if(ATCmdSvrRdy == TRUE)
	{
		Duster_module_Printf(1,"%s: atcmdsrv is ready",__FUNCTION__);
	}
	else
	{
		Duster_module_Printf(1,"%s: atcmdsrv is not ready",__FUNCTION__);
		return -1;
	}

	/*Do't do background scan for NezhaC*/
	at_channel = DUSTER_INIT_ATP_INDEX;
#ifdef LTE_BG_SCAN
	if(!PlatformIsNezhaC() && Action == DUSTER_CONFIG_START)
	{
		psm_set_wrapper(PSM_MOD_WAN,	"cellular",	"nw_apn",	"");
		sprintf(at_str, "AT+BGLTEPLMN?\r");
		ret = SendATCMDWaitResp(at_channel, at_str, 150, "+BGLTEPLMN",1,"+CME ERROR", resp_str, sizeof(resp_str));
		if(ret == 0)
		{
			Duster_module_Printf(2,"%s: AT+BGLTEPLMN? resp_str is %s",__FUNCTION__,resp_str);
			p1 = strstr(resp_str, ",");
			if((*(p1-1)-'0') == 1)      //enable BGLTEPLMN
			{
				Duster_module_Printf(2,"%s: enable BGLTEPLMN",__FUNCTION__);
				p2 = strtok(p1+1, ",");
				switch(atoi(p2))
				{
				case 0:
					sprintf(bg_time,"0");
					break;
				case 30:
					sprintf(bg_time,"1");
					break;
				case 60:
					sprintf(bg_time,"2");
					break;
				case 180:
					sprintf(bg_time,"3");
					break;
				case 300:
					sprintf(bg_time,"4");
					break;
				case 600:
					sprintf(bg_time,"5");
					break;
				case 900:
					sprintf(bg_time,"6");
					break;
				case 1800:
					sprintf(bg_time,"7");
					break;
				case 3600:
					sprintf(bg_time,"8");
					break;
				default:
					sprintf(bg_time,"9");
					break;
				}
				Duster_module_Printf(2,"%s: set bgscan_time with %s",__FUNCTION__,bg_time);
				psm_set_wrapper(PSM_MOD_WAN, "cellular", "bgscan_time",bg_time);
			}
			else
			{
				str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "bgscan_time");

				if(str != NULL)
				{
					CPUartLogPrintf("addr:0x%x,str%s",str,str);
				}

				switch(atoi(str))
				{
				case 0:
					BG_ScanTime = 0;                  // now
					enableflag  = 1;
					break;
				case 1:
					BG_ScanTime = 30;             // 30s
					enableflag  = 1;
					break;
				case 2:
					BG_ScanTime = 60;             // 1mins
					enableflag  = 1;
					break;
				case 3:
					BG_ScanTime = 180;             // 3mins
					enableflag  = 1;
					break;
				case 4:
					BG_ScanTime = 300;             // 5mins
					enableflag  = 1;
					break;
				case 5:
					BG_ScanTime = 600;             // 10mins
					enableflag  = 1;
					break;
				case 6:
					BG_ScanTime = 900;             // 15mins
					enableflag  = 1;
					break;
				case 7:
					BG_ScanTime = 1800;             // 30mins
					enableflag  = 1;
					break;
				case 8:
					BG_ScanTime = 3600;             // 60mins
					enableflag  = 1;
					break;
				case 9:
					BG_ScanTime = 0;                //disable
					enableflag  = 0;
					break;
				default:
					Duster_module_Printf(1, "%s: don't support input bgscan_time value", __FUNCTION__);
				}

				Duster_module_Printf(1,"%s: bgscan_time is %d",__FUNCTION__, atoi(str));
				if(atoi(str) != 9)
				{

					memset(at_str, '\0', 256);
					memset(resp_str, '\0', 256);
					sprintf(at_str, "AT+BGLTEPLMN=%d,%d\r", enableflag,BG_ScanTime);
					SendATCMDWaitResp(at_channel, at_str, 150, "+BGLTEPLMN",1,"+CME ERROR", resp_str, sizeof(resp_str));
					Duster_module_Printf(2, "resp_str: %s", resp_str);
					CPUartLogPrintf("resp_str: %s", resp_str);
					if(str)
					{
						CPUartLogPrintf("str addr:0x%x",str);
						duster_free(str);
						str = NULL;
					}
				}
				else
					Duster_module_Printf(2,"%s: BGLTEPLMN is disabled",__FUNCTION__);
			}
		}
	}
#endif
	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "disconnectnetwork_action");
	if(!strcmp(str,"1"))
	{
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "disconnectnetwork_action","0");
		str1 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "disconnectnetwork");
		if(str1 != NULL)
		{
			disconnectnetwork = atoi(str1);
			duster_free(str1);
			str1 = NULL;
		}
	}

	if(str)
	{
		duster_free(str);
		str = NULL;
	}

	str1 = psm_get_wrapper(PSM_MOD_WAN, NULL, "version_flag_action");
	if(!strcmp(str1,"1"))
	{
		psm_set_wrapper(PSM_MOD_WAN, NULL, "version_flag_action","0");

		str = psm_get_wrapper(PSM_MOD_WAN, NULL, "version_flag");
		if(str != NULL)
		{
			if(atoi(str) == 1)
			{
				Duster_module_Printf(1,"%s: set to LTG",__FUNCTION__);
				#if !defined(CRANE_WEBUI_SUPPORT)
				SwitchImage(PLATFORM_5MODE_LTG_VER, FALSE);
				#endif
			}
			else
			{
				Duster_module_Printf(1,"%s: set to LWG",__FUNCTION__);
				#if !defined(CRANE_WEBUI_SUPPORT)
				SwitchImage(PLATFORM_5MODE_LWG_VER, FALSE);
				#endif
			}
			UpdateVersionFlag = TRUE;

			duster_free(str);
			str = NULL;
		}
	}

	if(str1)
	{
		duster_free(str1);
		str1 = NULL;
	}

	str1 = psm_get_wrapper(PSM_MOD_WAN, NULL, "auto_switch_action");
	if(!strcmp(str1,"1"))
	{
		psm_set_wrapper(PSM_MOD_WAN, NULL, "auto_switch_action","0");

		if((PlatformIsZmifi() != TRUE) || (isSupportTds() == TRUE))    //forbid set auto-switch for non-TDS sim in ZIMI project.
		{
			str = psm_get_wrapper(PSM_MOD_WAN, NULL, "auto_switch");
			if(str != NULL)
			{
				set_auto_switch(atoi(str), FALSE);
				Duster_module_Printf(1,"%s: AutoSwitchFlag is %s %d",__FUNCTION__,str, switch_ctx.auto_switch);

				duster_free(str);
				str = NULL;
			}
		}
		else
			psm_set_wrapper(PSM_MOD_WAN, NULL, "auto_switch","0");
	}

	if(str1)
	{
		duster_free(str1);
		str1 = NULL;
	}


	if(Action == DUSTER_CONFIG_START && SelectNWFlag == FALSE)
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_select_done","0");

	//if manual search NW in idle state, then execute AT+COPS=?, and save result into PSM
	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "search_network");
	//Duster_module_Printf(1, "%s:select network is %s", __FUNCTION__,str);
	if(!strcmp(str,"1"))
	{
		//clear PLMN list displayed in WebUI
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "mannual_network_list","");
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "search_network","");
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_param","30");
		Duster_module_Printf(1, "%s: will trigger search network", __FUNCTION__);

		setSearchNetworkFlag(IND_REQ_HANDLE, SEARCH_NETWORK_START);

		MsgBuf = malloc(6);
		if(MsgBuf == NULL)
			ASSERT(0);

		//start to search network
		status_msg.MsgId = searchNW;
		status_msg.MsgData = MsgBuf;
		osaStatus = OSAMsgQSend(gWlanIndMSGQ, sizeof(status_msg), (UINT8 *)&status_msg, OSA_NO_SUSPEND);

		if(str)
		{
			duster_free(str);
			str = NULL;
		}

		/*wait 10 seconds*/
		OSATaskSleep(2000);
		return -1;
	}
	else
	{
		if(str != NULL)
		{
			Duster_module_Printf(1, "%s: search network is not triggered", __FUNCTION__);
			duster_free(str);
			str = NULL;
		}
	}

	ManualSelectNW = FALSE;

	//manual select PLMN
	memset(at_str, '\0', 256);
	memset(resp_str, '\0', 256);

	str1 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "network_param");
	str2 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "network_param_action");

	if((str2 != NULL) && (!strncmp(str2,"1",1)) && (str1 != NULL) && (strncmp(str1,"0",1)))
	{
		Duster_module_Printf(1, "%s: network_param %s, network_param_action %s", __FUNCTION__,str1, str2);
		Duster_module_Printf(1, "%s: network_param_action %s", __FUNCTION__,str2);

		psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_param_action","0");

		MsgBuf = malloc(6);
		if(MsgBuf == NULL)
			ASSERT(0);

		searchNW_tick = 0;
		if(!strncmp(str1,"30",2))          //auto select NW
		{
			Duster_module_Printf(2, "%s: auto select NW", __FUNCTION__);
			//clear PLMN list displayed in WebUI
			psm_set_wrapper(PSM_MOD_WAN, "cellular", "mannual_network_list",""); /*clean NW search Result 495142*/
			psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_param",	"30");/*set selectable NW to AUTO*/
			sprintf(at_str, "AT+COPS=0\r");
			ret = SendATCMDWaitResp(at_channel, at_str, 150, NULL,1,"+CME ERROR", resp_str, sizeof(resp_str));

			//if(ret == 0)
			{
				Duster_module_Printf(1, "PSDetachFlag=%d",PSDetachFlag);
				if(PSDetachFlag == TRUE)
				{
					PSDetachFlag = FALSE;
					OSATaskSleep(100);     //sleep 1s
					sprintf(at_str, "AT+CGATT=1\r");
					SendATCMDWaitResp(at_channel,at_str, 150, NULL,0, "+CME ERROR", resp_str, sizeof(resp_str));
					OSATaskSleep(100);
				}
				setSearchNetworkFlag(IND_REQ_HANDLE, SEARCH_NETWORK_SELECT_DONE);

				psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_select_done","1");
				OSATaskSleep(1000);

				cellularFlag = FALSE;

				status_msg.MsgId = CellularStart;
				status_msg.MsgData = MsgBuf;
				osaStatus = OSAMsgQSend(gWlanIndMSGQ, sizeof(status_msg), (UINT8 *)&status_msg, OSA_NO_SUSPEND);

			}
			/*
					   else
			       	      Duster_module_Printf(2, "AT+COPS=0 fails");
			*/

		}
		else           //manual select NW
		{
			p1=strstr(str1, "%");
			memcpy(Act, p1+1, 1);
			p2=strstr(p1+1, "%");
			memcpy(PLMN, p2+1, 5);

			sprintf(at_str, "AT+COPS=1,2,%s,%s\r", PLMN, Act);
			Duster_module_Printf(2, "%s: maunal select NW with %s", __FUNCTION__, at_str);
			ret = SendATCMDWaitResp(at_channel, at_str, 150, NULL,1,"+CME ERROR", resp_str, sizeof(resp_str));
			//if(ret == 0)
			{
				Duster_module_Printf(1, "PSDetachFlag=%d",PSDetachFlag);
				if(PSDetachFlag == TRUE)
				{
					PSDetachFlag = FALSE;
					OSATaskSleep(100);     //sleep 1s
					sprintf(at_str, "AT+CGATT=1\r");
					SendATCMDWaitResp(at_channel,at_str, 150, NULL,0, "+CME ERROR", resp_str, sizeof(resp_str));

					OSATaskSleep(100);	   //sleep 1s
				}
				OSATaskSleep(1000);     //sleep 5s

				setSearchNetworkFlag(IND_REQ_HANDLE, SEARCH_NETWORK_SELECT_DONE);
				psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_select_done","1");

				//clear PLMN list displayed in WebUI
				//psm_set_wrapper(PSM_MOD_WAN, "cellular", "mannual_network_list","");/*keep NW search Result*/

				cellularFlag = TRUE;

				status_msg.MsgId = CellularStart;
				status_msg.MsgData = MsgBuf;
				osaStatus = OSAMsgQSend(gWlanIndMSGQ, sizeof(status_msg), (UINT8 *)&status_msg, OSA_NO_SUSPEND);

			}
			/*
					   else
					   	 psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_select_done","2");
			*/

		}

			ManualSelectNW = TRUE;

	}

	if(str1)
	{
		duster_free(str1);
		str1 = NULL;
	}

	if(str2)
	{
		duster_free(str2);
		str2 = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "bgscan_time_action");
	if(!strcmp(str,"1") && !PlatformIsNezhaC())
	{
		psm_set_wrapper(PSM_MOD_WAN, "cellular", "bgscan_time_action","0");

		//send bgscan_time to CP
		str1 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "bgscan_time");
		if((strncmp(str1,"",1)) && (str1 != NULL))
		{
			Duster_module_Printf(2,"%s: bgscan_time is %d",__FUNCTION__, atoi(str1));

			//if((gPSActDetail == 7))
			{
				switch(atoi(str1))
				{
				case 0:
					BG_ScanTime = 0;              // now
					enableflag  = 1;
					break;
				case 1:
					BG_ScanTime = 30;             // 30s
					enableflag  = 1;
					break;
				case 2:
					BG_ScanTime = 60;             // 1mins
					enableflag  = 1;
					break;
				case 3:
					BG_ScanTime = 180;             // 3mins
					enableflag  = 1;
					break;
				case 4:
					BG_ScanTime = 300;             // 5mins
					enableflag  = 1;
					break;
				case 5:
					BG_ScanTime = 600;             // 10mins
					enableflag  = 1;
					break;
				case 6:
					BG_ScanTime = 900;             // 15mins
					enableflag  = 1;
					break;
				case 7:
					BG_ScanTime = 1800;            // 30mins
					enableflag  = 1;
					break;
				case 8:
					BG_ScanTime = 3600;            // 60mins
					enableflag  = 1;
					break;
				case 9:
					BG_ScanTime = 0;             //disable
					enableflag  = 0;
					break;
				default:
					Duster_module_Printf(1, "%s: don't support input bgscan_time value", __FUNCTION__);
				}

				memset(at_str, '\0', 256);
				memset(resp_str, '\0', 256);
				sprintf(at_str, "AT+BGLTEPLMN=%d,%d\r", enableflag,BG_ScanTime);
				// if(enable_bgscan == 1)
				SendATCMDWaitResp(at_channel, at_str, 150, "+BGLTEPLMN",1,"+CME ERROR", resp_str, sizeof(resp_str));
				Duster_module_Printf(2, "resp_str: %s", resp_str);

				duster_free(str1);
				str1 = NULL;

				if(str)
				{
					duster_free(str);
					str = NULL;
				}

				if(Action != DUSTER_CONFIG_START)
					return -1;
			}
		}

		if(str1)
		{
			duster_free(str1);
			str1 = NULL;
		}

	}

	if(str)
	{
		duster_free(str);
		str = NULL;
	}

	//judge how to search NW when power on
	Duster_module_Printf(1, "%s:judge how to select NW when power on", __FUNCTION__);
	if(Action != DUSTER_CONFIG_START)
	{
		str = psm_get_wrapper(PSM_MOD_WAN, "cellular", "manual_network_start");
		if(!strncmp(str,"1",1))
		{
			Duster_module_Printf(2,"%s: manual_network_start is %d",__FUNCTION__, atoi(str));
		}

		if(str!=NULL)
		{
			duster_free(str);
			str = NULL;
		}
	}

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "Engineering_mode");
	if(str != NULL)
	{
		if((EngiMode != atoi(str)) && (atoi(str) == 0))        //disable Engineering_mode
		{
			//disable engineering mode setting
			memset(resp_str, '\0', 256);
			SendATCMDWaitResp(at_channel, "AT+EEMOPT=0\r", 150, NULL,1,"+CME ERROR", resp_str, sizeof(resp_str));

			//restore NW setting
			memset(at_str, '\0', 256);
			memset(resp_str, '\0', 256);
			Duster_module_Printf(1,	"%s restore NW setting, LTE_W_PS Flag:%d",__FUNCTION__,IS_LTE_W_PS);
			if(IS_LTE_W_PS)
			{
				sprintf(at_str, "AT*BAND=%d,0,0,0,0,2,4,0\r", NWmode);
			}
			else
			{
				sprintf(at_str, "AT*BAND=%d,0,2,4,0\r", NWmode);
			}
			SendATCMDWaitResp(at_channel, at_str, 150, NULL,1,"+CME ERROR", resp_str, sizeof(resp_str));
		}

		EngiMode = atoi(str);
		Duster_module_Printf(1, "%s: EngiMode is %d", __FUNCTION__,EngiMode);
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "query_time_interval");
	if(str != NULL)
	{
		QueryInterval = atoi(str);
		Duster_module_Printf(1, "%s: QueryInterval is %d", __FUNCTION__,QueryInterval);
		duster_free(str);
		str = NULL;
	}


	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "proto");
	Duster_module_Printf(1, "%s: proto %s", __FUNCTION__,str);
	//checkRC(str);
	if(strcmp(str, "cellular") == 0)
	{
		duster_free(str);
		str = NULL;

		// Roaming disable auto dial setting
		str2 = psm_get_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_auto_dial_action");
		str1 = psm_get_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_auto_dial");

		// Roaming disable dial setting
		str4 = psm_get_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_dial_action");
		str3 = psm_get_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_dial");

		if(!strcmp(str2,"1") || !strcmp(str4,"1")||(Action == DUSTER_CONFIG_START))
		{
			psm_set_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_auto_dial_action","0");
			psm_set_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_dial_action", "0");
			if(str1 != NULL || str3 != NULL)
			{
				Duster_module_Printf(1, "%s: set Roaming_disable_auto_dial flag %d", __FUNCTION__, atoi(str1));
				Duster_module_Printf(1, "%s: set Roaming_disable_dial flag %d", __FUNCTION__, atoi(str3));

				if(!strcmp(str2,"1"))
				{
					if(str1 != NULL)
						roaming_status = str1[0]-'0';
				}
				if(!strcmp(str4,"1"))
				{
					if(str3 != NULL)
						roaming_status = str3[0]-'0';
				}
				CM_Set_ConnectType_WEBUI(roaming_status);
			}
			/***rjin 20140226*****/
			/*When prohit dial in roaming is checked, we should destroy all PDP*/
			/*When enable dial in roamin, we should create new PDP context to triggle dialer*/
			if(Action == DUSTER_CONFIG_SET)
			{
			    #if defined(CRANE_WEBUI_SUPPORT)
			    if((str1 != NULL || str3 != NULL) && (CIMM_REGSTATUS_ROAMING == get_current_reg_status(0)))
			    #else
				if((str1 != NULL || str3 != NULL) && (CIMM_REGSTATUS_ROAMING == get_current_reg_status()))
				#endif
				{
					if(!strncmp(str1,"1",1) || !strncmp(str3,"1",1))
					{
						Duster_module_Printf(1, "%s: prohit dial in roaming, will destroy PDP", __FUNCTION__);
						// destroy all PDP
						int connection_num;
						for(connection_num = 1; connection_num <= MAX_MO_PDP_NUMBER || (connection_num >=MIN_CONNUM_CREATBY_NETWORK && connection_num < MIN_CONNUM_CREATBY_NETWORK+MAX_MT_PDP_NUMBER); connection_num++) {
							wan_cellular_stop_pdp(connection_num);
							#if defined(CRANE_WEBUI_SUPPORT)
							if(isLTENetwork(0))
							#else
							if(isLTENetwork())
							#endif
							{
								if(connection_num == MAX_MO_PDP_NUMBER)
									connection_num = MIN_CONNUM_CREATBY_NETWORK-1;

							}
						}
					}
					else
					{
						// enable  PDP
						Duster_module_Printf(1, "%s: allow dial in roaming, will create PDP", __FUNCTION__);
						wan_cellular_start_cm(Action, at_channel);
					}

					if(str1 != NULL)
					{
						duster_free(str1);
						str1 = NULL;
					}
					if(str2 != NULL)
					{
						duster_free(str2);
						str2 = NULL;
					}
					if(str3 != NULL)
					{
						duster_free(str3);
						str3 = NULL;
					}
					if(str4 != NULL)
					{
						duster_free(str4);
						str4 = NULL;
					}
					return 0;
				}
			}
			/****End*****/
		}

		if(str1 != NULL)
		{
			duster_free(str1);
			str1 = NULL;
		}
		if(str2 != NULL)
		{
			duster_free(str2);
			str2 = NULL;
		}
		if(str3 != NULL)
		{
			duster_free(str3);
			str3 = NULL;
		}
		if(str4 != NULL)
		{
			duster_free(str4);
			str4 = NULL;
		}


		str2 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "NW_mode_action");

		if(!strcmp(str2,"1"))
		{
			config_NW = TRUE;

			psm_set_wrapper(PSM_MOD_WAN, "cellular", "NW_mode_action","0");
		}

		if(str2)
		{
			duster_free(str2);
			str2 = NULL;
		}

		str1 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "NW_mode");
		disable_NW = FALSE;
		WebUINWmode = atoi(str1);
		if(str1 != NULL)
		{
			switch(atoi(str1))
			{
			case 1:           //triple mode
				NWmode = CI_DEV_NW_TRIP_MODE_LTE;
				break;

			case 2:           //4G only
				NWmode = CI_DEV_NW_LTE_MODE;
				psm_set_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode","0");
				break;

			case 3:           //4G/3G mode
				NWmode = CI_DEV_NW_DUAL_UMTS_LTE_MODE_LTE;
				break;

			case 4:           //3G/2G mode
				NWmode = CI_DEV_NW_DUAL_GSM_UMTS_MODE_UMTS;
				break;

			case 5:           //3G only
				NWmode = CI_DEV_NW_UMTS_MODE;
				psm_set_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode","0");

				break;

			case 6:           //2G only
				NWmode = CI_DEV_NW_GSM_MODE;
				psm_set_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode","0");
				break;

			case 7:           //disable NW setting
				disable_NW = TRUE;
				break;

			case 8:
				NWmode = CI_DEV_NW_DUAL_GSM_LTE_MODE;
				break;
#ifdef QUECTEL_PROJECT_CUST
			case 9:			//5G/4G mode
				NWmode = CI_DEV_NW_LTE_NR_MODE;
				break;
			case 10:		//5G only
				NWmode = CI_DEV_NW_NR_MODE;
				break;
			case 11:		//LTE only
				NWmode = CI_DEV_NW_LTE_MODE;
				break;
#endif
			default:
				Duster_module_Printf(1,"%s: inputed NW_mode is not supported",__FUNCTION__);
				break;
			}
		}
		Duster_module_Printf(1, "%s: nw_mode(webui) %d, nw_mode(CI) is %d, disable_NW is %d, config_NW is %d", __func__, WebUINWmode, NWmode, disable_NW,config_NW);

		if(str1)
		{
			duster_free(str1);
			str1 = NULL;
		}

		str2 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode_action");
		if(!strcmp(str2,"1"))
		{
			psm_set_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode_action","0");

			config_NW = TRUE;
		}

		if(str2)
		{
			duster_free(str2);
			str2 = NULL;
		}

		str1 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "prefer_mode");
		WebUIPreferMode = atoi(str1);
		if(str1 != NULL)
		{
			switch(atoi(str1))
			{
			case 1:           //4G prefer(Triple mode)
				NWmode = CI_DEV_NW_TRIP_MODE_LTE;
				break;

			case 2:           //3G prefer(Triple mode)
				NWmode = CI_DEV_NW_TRIP_MODE_UMTS;
				break;

			case 3:           //4G prefer (3G/4G)
				NWmode = CI_DEV_NW_DUAL_UMTS_LTE_MODE_LTE;
				break;

			case 4:           //3G prefer (3G/4G)
				NWmode = CI_DEV_NW_DUAL_UMTS_LTE_MODE_UMTS;
				break;

			case 5:           //3G prefer (2G/3G)
				NWmode = CI_DEV_NW_DUAL_GSM_UMTS_MODE_UMTS;
				break;

			case 6:           //2G prefer (2G/3G)
				NWmode = CI_DEV_NW_DUAL_GSM_UMTS_MODE_GSM;
				break;

			case 7:           //2G prefer(Triple mode)           //need webUI add this item
				NWmode = CI_DEV_NW_TRIP_MODE_GSM;
				break;

			case 8:
				NWmode = CI_DEV_NW_DUAL_GSM_LTE_MODE_GSM;
				break;

			case 9:
				NWmode = CI_DEV_NW_DUAL_GSM_LTE_MODE_LTE;
				break;
#ifdef QUECTEL_PROJECT_CUST
			case 10:		//5G prefer
				NWmode = CI_DEV_NW_LTE_NR_MODE_NR;
				break;
			case 11:		//4G prefer
				NWmode = CI_DEV_NW_LTE_NR_MODE_LTE;
				break;
#endif
			default:
				Duster_module_Printf(1,"%s: inputed prefer_mode is not supported",__FUNCTION__);
				break;
			}
		}

		Duster_module_Printf(1,"%s: prefer_mode(webui) %d, NWmode %d, disable_NW %d, config_NW %d", __func__, WebUIPreferMode,  NWmode, disable_NW, config_NW);

		if(str1)
		{
			duster_free(str1);
			str1 = NULL;
		}

		str2 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "prefer_lte_type_action");
		if(!strcmp(str2,"1"))
		{
			psm_set_wrapper(PSM_MOD_WAN, "cellular", "prefer_lte_type_action","0");

			config_NW = TRUE;
		}

		if(str2)
		{
			duster_free(str2);
			str2 = NULL;
		}

		str1 = psm_get_wrapper(PSM_MOD_WAN, "cellular", "prefer_lte_type");
		if((str1 != NULL) && (PreferLteType != atoi(str1)))
		{
		    Duster_module_Printf(1,"%s: prefer_lte_type is %s",__FUNCTION__,str1);
			switch(atoi(str1))
			{
			case 0:           //TD-LTE
			case 1:           //FDD-LTE
				bandPriorityFlag = atoi(str1)+1;
				Duster_module_Printf(1,"%s: bandPriorityFlag is %d",__FUNCTION__,bandPriorityFlag);

				memset(at_str, '\0', DIALER_MSG_SIZE);
				memset(resp_str, '\0', DIALER_MSG_SIZE);

				sprintf(at_str, "AT*BAND?\r");
				ret = SendATCMDWaitResp(at_channel, at_str, 150, "*BAND" ,1,"+CME ERROR", resp_str, sizeof(resp_str));
				if(ret != 0)
					break;

				Duster_module_Printf(1,"at*band: resp_str is %s",resp_str);

                #if !defined(CRANE_WEBUI_SUPPORT)
				if(sscanf(resp_str, "%*[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%*s", GsmBand, UmtsBand, LTEBandH, LTEBandL, roamingConfig, srvDomain, bandPriorityFlag_tmp, LteBandExt) == 8)
				{
					G_band = atoi(GsmBand);
					U_band = atoi(UmtsBand);
					L_bandh = atoi(LTEBandH);
					L_bandl = atoi(LTEBandL);
					ltebandext = atoi(LteBandExt);
					Duster_module_Printf(1,"at*band: %s %s %s %s %s",GsmBand, UmtsBand, LTEBandH, LTEBandL,LteBandExt);
					Duster_module_Printf(1,"at*band: %d %d %d %d %d",G_band, U_band, L_bandh, L_bandl,ltebandext);
				}
				else
				{
					Duster_module_Printf(1,"%s: fail to parse band",__FUNCTION__);
				}
				#endif

				break;

			default:
				Duster_module_Printf(1,"%s: inputed bandPriorityFlag is not supported",__FUNCTION__);
				break;
			}
		}


		if(str1)
		{
			duster_free(str1);
			str1 = NULL;
		}


		psNVMSetting = psNvmCheckUserModeConfigInfo(WebUINWmode, WebUIPreferMode,	bandPriorityFlag);
        Duster_module_Printf(1,"%s: psNVMSetting %d",__FUNCTION__, psNVMSetting);
		if(((Action == DUSTER_CONFIG_SET) && (disable_NW == FALSE) && (config_NW == TRUE)) && !psNVMSetting|| ((Action == DUSTER_CONFIG_START)&&(disable_NW == FALSE)&&!psNVMSetting))
		{
			memset(at_str, '\0', 256);
			memset(resp_str, '\0', 256);
			Duster_module_Printf(1,	"%s send AT*BAND:LTE_W_PS Flag:%d",__FUNCTION__,IS_LTE_W_PS);
			if(IS_LTE_W_PS)
			{

				sprintf(at_str, "AT*BAND=%d,%d,%d,%d,%d,2,4,%d,%d\r", NWmode,G_band,U_band,L_bandh,L_bandl,bandPriorityFlag,ltebandext);
			}
			else
			{
				sprintf(at_str, "AT*BAND=%d,0,2,4,%d\r", NWmode,bandPriorityFlag);
			}
			/*Clean PS IND	start*/
			clearUnknownPdpCause(IND_REQ_HANDLE, SYS_MODE_MAX);
			clearErrorIndFlagAll();
			/*Clean PS IND	end*/
			SendATCMDWaitResp(at_channel, at_str, 150, NULL,1,"+CME ERROR", resp_str, sizeof(resp_str));
			Duster_module_Printf(2, "resp_str: %s", resp_str);
		}
		else if(disable_NW == TRUE)
		{
			Duster_module_Printf(1, "%s: auto select network!",__FUNCTION__);
			sprintf(at_str, "AT*BAND=12\r");
			/*Clean PS IND	start*/
			clearUnknownPdpCause(IND_REQ_HANDLE, SYS_MODE_MAX);
			clearErrorIndFlagAll();
			/*Clean PS IND	end*/
			SendATCMDWaitResp(at_channel, at_str, 150, NULL,1,"+CME ERROR", resp_str, sizeof(resp_str));
			Duster_module_Printf(2, "%s: %s resp[%s]",__FUNCTION__,at_str,resp_str);
		}
		else
		{
			Duster_module_Printf(1, "%s Don't  send AT*BAND",__FUNCTION__);
		}

	}
	if(str)
	{
		duster_free(str);
		str = NULL;
	}
	wan_post_set_cm(Action,at_channel);
	wan_set_mtu_lwip(Action);

	Duster_module_Printf(1,"leave %s",__FUNCTION__);
	return 0;
}

int SearchNW(void)
{
#define SEARCHNW_RESULT_MAX_LEN 512
#define MAX_NW_RECORD_LIST_BUF 512
#define MAX_NW_RECORD_BUF 64

	char at_str[DIALER_MSG_SIZE] = {'\0'};
	char *resp_str = NULL;
	int ret, i,j;
	char *nxtStr= NULL;
	char *tmp=NULL, *pos=NULL;
	char NW_buf[512]= {0};
	char tmp_buf[MAX_NW_RECORD_BUF]= {0};
	char fd, rd;
	char ISP_name_long[32]= {0};
	char ISP_name_short[32]= {0};
	char PLMN_name_tmp[8]= {0};
	char ISP_name[32]= {0};
	char PLMN_name[8]= {0};
	int Act;
	OSA_STATUS osa_status;
	char *startStr = NULL;
	int supp_ope_num, size = 0;
	int cid = 0;
	CM_Connection_Context *ctx = NULL;
	char conn_num = 0;
	bool need_bring_up = FALSE;

	resp_str = duster_malloc(SEARCHNW_RESULT_MAX_LEN);
	if(resp_str == NULL)
		ASSERT(0);

    searchNW_tick = 0;       //fix URT pxa1802 LWG-1617(39378)

#if 0
    /* tear down netif */
    for (cid = 0; cid < TEL_AT_PS_CID_VAL_MAX; cid++)
    {
       ctx = CM_GetContextWithCid(cid);
       if (ctx)
       {
           configureLwipNetif(0xFF, cid, 1);
       }
    }
#endif

    /* destroy pdp and tear down netif */
    conn_num = CM_Get_ConnectionNum(getDefaultCidNum() - 1);

    ctx = CM_GetContextWithCid(conn_num);
    if (ctx)
    {
        wan_cellular_stop_pdp(conn_num);
        need_bring_up = TRUE;
    }
    else
    {
        ctx = CM_Exist_DefaultPdp();
        if (ctx)
        {
            conn_num = ctx->connection_num;
            wan_cellular_stop_pdp(conn_num);
            need_bring_up = TRUE;
        }
    }

    if (need_bring_up == TRUE)
    {
        int wait_times = 30;
        while(wait_times--)
        {
            ctx = CM_GetContextWithCid(conn_num);
            if (ctx)
            {
                if (ctx->connect_status == 0)
                    break;
                else
                    OSATaskSleep(200);

            }
            else
                break;
        }
    }

#if 0
	memset(resp_str, 0, SEARCHNW_RESULT_MAX_LEN);
	memset(at_str, '\0', DIALER_MSG_SIZE);
	sprintf(at_str, "AT+COPS=2\r");
	ret = SendATCMDWaitResp(EVENT_HANDLER_ATP_INDEX, at_str, 150, NULL, 1, "+CME ERROR", resp_str, SEARCHNW_RESULT_MAX_LEN);
	if (ret != 0)
	{
		Duster_module_Printf(1, "%s: get error for send [%s], ret %d", __func__, at_str, ret);
		goto try_again;
	}
	else
		Duster_module_Printf(1, "%s: resp_str [%s] for send [%s]", __func__, resp_str, at_str);

    OSATaskSleep(200);	   //sleep 1s
#endif
	memset(at_str, '\0', DIALER_MSG_SIZE);
	memset(resp_str, '\0', SEARCHNW_RESULT_MAX_LEN);
	sprintf(at_str, "AT+COPS=?\r");
	ret = SendATCMDWaitResp(EVENT_HANDLER_ATP_INDEX, at_str, 300, "+COPS",1,"+CME ERROR", resp_str, SEARCHNW_RESULT_MAX_LEN);
	/* set cops rsp flag to true */
	cops_rsp_flag = TRUE;

	if (ret != 0)
	{
		Duster_module_Printf(1, "%s: get error for send [%s], ret %d", __func__, at_str, ret);
		goto try_again;
	}
	else
	{
		if (strlen(resp_str) < 160)
			Duster_module_Printf(1, "%s: resp_str: %s", __func__, resp_str);
	}

	if(ret == 0)
	{
		searchNW_exit_flag = TRUE;
		setSearchNetworkFlag(IND_REQ_HANDLE, SEARCH_NETWORK_SELECT_START);

		getTotalSuppOpeNum(1, (INT32 *)&supp_ope_num);
		Duster_module_Printf(1, "%s: gTotalSuppOpeNum: %d", __func__, supp_ope_num);

		duster_call_module_get_delimiters(PSM_MOD_WAN, &fd, &rd);

		startStr = &resp_str[6];  //omit "+cops:"

		if(supp_ope_num)
			searchNW_tick = OSAGetTicks();

		for(i=0; i<supp_ope_num; i++)
		{
			memset(ISP_name_long,0,32);
			memset(ISP_name_short,0,32);
			memset(ISP_name,0,32);

			memset(PLMN_name,0,8);
			memset(PLMN_name_tmp,0,8);
			memset(tmp_buf, 0, MAX_NW_RECORD_BUF);

			nxtStr = strstr(startStr, "),") + 2;
			if((nxtStr-startStr+1) > 0)
			{
				for(j=0; j<(nxtStr-startStr+1); j++)
					Duster_module_Printf(4,"%c %x",startStr[j],startStr[j]);
			}

			//check whether operation str is in normal format.
			if(sscanf(startStr, "%*[^,], %[^,], %[^,], %[^,], %[^)]),%*[^]", ISP_name_long, ISP_name_short, PLMN_name_tmp, tmp_buf) == 4)
			{
				Duster_module_Printf(1,"%s: ISP_name_long = %s, ISP_name_short = %s, PLMN_name = %s, Act = %s", __func__, ISP_name_long, ISP_name_short, PLMN_name_tmp, tmp_buf);

				Act = atoi(tmp_buf); //omit ' ' in tmp_buf

				if(strlen(ISP_name_short) > 2)
				{
					memcpy(ISP_name, &ISP_name_short[1], strlen(ISP_name_short)-2);
				}
				else
				{
					memcpy(ISP_name, &ISP_name_long[1], strlen(ISP_name_long)-2);
				}

				//omit '"'' in ISP_name
				for(j=0; j<strlen(ISP_name); j++)
				{
					if(ISP_name[j] == ' ')
						ISP_name[j] = '-';
				}

				memcpy(PLMN_name, PLMN_name_tmp+1, strlen(PLMN_name_tmp)-2);

				Duster_module_Printf(1,"%s: ISP_name = %s, PLMN_name = %s, Act = %d", __func__, ISP_name, PLMN_name, Act);

				snprintf(tmp_buf, MAX_NW_RECORD_BUF, "%s%c%d%c%s%c", ISP_name,fd,Act,fd,PLMN_name,rd);
				size += snprintf(NW_buf + size, MAX_NW_RECORD_LIST_BUF - size, "%s", tmp_buf);

				/*added by shoujunl 131211 start -- Keep WebUI do not display AUTO if user do not select AUTO start*/
				if(i == 0)
				{
					memset(tmp_buf,	0, MAX_NW_RECORD_BUF);
					sprintf(tmp_buf,	"%s%c%d%c%s",	ISP_name,fd,Act,fd,PLMN_name);
					psm_set_wrapper(PSM_MOD_WAN, "cellular", "network_param",	tmp_buf);
				}
				/*added by shoujunl 131211 start -- Keep WebUI do not display AUTO if user do not select AUTO end*/
			}
			else
				Duster_module_Printf(1,"%s: fail to parse", __func__);

			startStr = nxtStr;
		}
		if (strlen(NW_buf) < 160)
			Duster_module_Printf(1, "%s: NW_buf %s", __func__, NW_buf);

		psm_set_wrapper(PSM_MOD_WAN, "cellular", "mannual_network_list", NW_buf);
		resetTotalSuppOpeNum(1);
		goto end;
	}

try_again:
	/* no need to retry search network if failed, just return fail to WebUI */
	#if 0
	if(gSearchNWTask == NULL)
	{
		searchNW_exit_flag = FALSE;

		osa_status  = OSATaskCreate(&gSearchNWTask,
		                            gSearchNWTaskStack,
		                            SEARCHNWTASKSTACKSIZE,
		                            128,
		                            (char *)"SearchNWTask",
		                            SearchNetworkTask,
		                            (void *)0);


		if ( osa_status != OS_SUCCESS)
		{
			Duster_module_Printf(1,"OSATaskCreate error!");
			ASSERT(0);
		}

		Duster_module_Printf(1,"%s: create gSearchNWTask to seach NW cause current search failed", __func__);
	}
	#endif
	psm_set_wrapper(PSM_MOD_WAN, "cellular", "mannual_network_list", "failed");
	setSearchNetworkFlag(IND_REQ_HANDLE, SEARCH_NETWORK_INIT);
end:
	if(resp_str)
	{
		duster_free(resp_str);
		resp_str = NULL;
	}

#if 0
    /* bring up netif */
    for (cid = 0; cid < TEL_AT_PS_CID_VAL_MAX; cid++)
    {
       ctx = CM_GetContextWithCid(cid);
       if (ctx)
       {
           configureLwipNetif(1, cid, 1);
       }
    }
#endif

    if (need_bring_up)
    {
        CM_Start_Exist_Data_Connection(conn_num, TRUE);
    }

	Duster_module_Printf(1,"leave %s,  ret %d", __func__, ret);
	return ret;
}

/*
void wan_add_device(DeviceNode *device, int deviceType)
{
	int i,j, match=0;
	sta_list *ptr=NULL;
	char *mac = NULL, *wlan_mac=NULL;
	char* raw = NULL;
       UINT32  FirstValidIndex=0xff;

       Duster_module_Printf(1,"%s: add device, deviceType is %d",__FUNCTION__ ,deviceType);
       Duster_module_Printf(1,"%s: mac is %02X:%02X:%02X:%02X:%02X:%02X",__FUNCTION__ , device->MAC[0],device->MAC[1], \
        	device->MAC[2],device->MAC[3],device->MAC[4],device->MAC[5]);
       Duster_module_Printf(1,"%s: IP is %d.%d.%d.%d",__FUNCTION__ , device->IP[0],device->IP[1], \
        	device->IP[2],device->IP[3]);
       Duster_module_Printf(1,"%s: name is %s",__FUNCTION__ , device->name);

	if(deviceType == DeviceType_PC)
	{
		cur_tick = OSAGetTicks();
		memset(&(gUI2DialerPara.PC), 0, sizeof(DeviceNode));
		memcpy(&(gUI2DialerPara.PC), device, sizeof(DeviceNode));
		gUI2DialerPara.PC.Reg_Tick = cur_tick;
		duster_Printf("add USB device with register time is %lu", cur_tick);
	}
	else if(deviceType == DeviceType_WlanClient)
	{
	       mac = ConvertASCtoString(device->MAC, ConvertType_MAC);

	       if(gUI2DialerPara.ActiveWlanClientNumber >=10)
	       {
	       	duster_Printf("%s: Active Wlan Client Number reach Max number", __FUNCTION__);
			if(mac)
				duster_free(mac);
	       	return;
	       }

		duster_Printf("wan_add_device: device MAC is %s",mac);

	       duster_Printf("ActiveWlanClientNumber is %d",gUI2DialerPara.ActiveWlanClientNumber);
	       duster_Printf("GetWlanClientNum: %d", GetWlanClientNum());

		for(i=0;i< 10; i++)
		{
		        raw = gUI2DialerPara.WlanClient[i].MAC;
			Duster_module_Printf(7, "%s: mac of WlanClient%d in DDR is %02x:%02x:%02x:%02x:%02x:%02x",__FUNCTION__, i, (unsigned int) raw[0],
                            (unsigned int) raw[1], (unsigned int) raw[2], (unsigned int) raw[3],(unsigned int) raw[4], (unsigned int) raw[5]);

		}


	       //add new device.
		for(i=0;i< 10; i++)
		{
			if((gUI2DialerPara.WlanClient[i].MAC[0]==0) && (gUI2DialerPara.WlanClient[i].MAC[1]==0) && (gUI2DialerPara.WlanClient[i].MAC[2]==0) && \
				(gUI2DialerPara.WlanClient[i].MAC[3]==0) &&(gUI2DialerPara.WlanClient[i].MAC[4]==0) &&(gUI2DialerPara.WlanClient[i].MAC[5]==0) )
			{
			      if(FirstValidIndex == 0xff)
			        	FirstValidIndex = i;
			      continue;
			}

		       wlan_mac = ConvertASCtoString(gUI2DialerPara.WlanClient[i].MAC,  ConvertType_MAC);
		       //duster_Printf("wan_add_device: WlanClient%d MAC is %s", i, wlan_mac);
			if(!strcasecmp(mac, wlan_mac))
			{
			match = 1;
			memcpy(gUI2DialerPara.WlanClient[i].IP, device->IP, 4);
			memcpy(gUI2DialerPara.WlanClient[i].MAC, device->MAC, 6);
			memcpy(gUI2DialerPara.WlanClient[i].name, device->name, 20);
			}

			if(wlan_mac != NULL)
			{
				duster_free(wlan_mac);
				wlan_mac = NULL;
			}

		}

		if(!match)
		{
			memcpy(gUI2DialerPara.WlanClient[FirstValidIndex].IP, device->IP, 4);
			memcpy(gUI2DialerPara.WlanClient[FirstValidIndex].MAC, device->MAC, 6);
			memcpy(gUI2DialerPara.WlanClient[FirstValidIndex].name, device->name, 20);

		       duster_Printf("mac of WlanClient%d is %s",FirstValidIndex, mac);
		       FirstValidIndex = 0xff;
		}

		if(mac != NULL)
		{
			duster_free(mac);
			mac = NULL;
		}



	}
	else
		duster_Printf("%s: incorrect device type", __FUNCTION__);


}
*/
//get connected pc number, 0 or 1
int wan_get_pcdevice(void)
{
	if((gUI2DialerPara.PC.MAC[0] == 0) && (gUI2DialerPara.PC.MAC[1] == 0) && (gUI2DialerPara.PC.MAC[2] == 0) && \
	        (gUI2DialerPara.PC.MAC[3] == 0) && (gUI2DialerPara.PC.MAC[4] == 0) && (gUI2DialerPara.PC.MAC[5] == 0))
	{
		return 0;
	}
	else
	{
		return 1;
	}
}

void wan_delete_device(DeviceNode *device, int deviceType)
{
	int i,j,match=0;
	OSA_STATUS osa_status;
	WlanClientStatusMessage msg_buf;
	//Duster_module_Printf(1,"%s: delete device, deviceType is %d",__FUNCTION__ ,deviceType);
	Duster_module_Printf(2,"%s: mac is %02X:%02X:%02X:%02X:%02X:%02X",__FUNCTION__ , device->MAC[0],device->MAC[1], \
	                     device->MAC[2],device->MAC[3],device->MAC[4],device->MAC[5]);

	if(deviceType == DeviceType_PC)
	{
		Duster_module_Printf(1, "%s: delete USB mac %02X:%02X:%02X:%02X:%02X:%02X",__FUNCTION__ , device->MAC[0],device->MAC[1], \
	                     device->MAC[2],device->MAC[3],device->MAC[4],device->MAC[5]);
		memset(&(gUI2DialerPara.PC), 0, sizeof(DeviceNode));
		msg_buf.MsgId = lwip_delete_PC;
		msg_buf.MsgData = (char *)device;
		osa_status = OSAMsgQSend(gWlanIndMSGQ, sizeof(WlanClientStatusMessage), (UINT8 *)&msg_buf, OSA_NO_SUSPEND);

		if(osa_status != OS_SUCCESS )
		{
			Duster_module_Printf(1,"%s: OSAMsgQSend fail. osa_status is %u",__FUNCTION__ , osa_status);
			if(device)
				free(device);
		}

	}
#ifdef WIFI_SUPPORT
	else if(deviceType == DeviceType_WlanClient)
	{
		if(gUI2DialerPara.ActiveWlanClientNumber >10)
		{
			duster_Printf("%s: Active Wlan Client  reach Max number", __FUNCTION__);
			if(device)
				free(device);
			return;
		}

		for(i=0; i< 10; i++)
		{
			if((gUI2DialerPara.WlanClient[i].MAC[0]==0) && (gUI2DialerPara.WlanClient[i].MAC[1]==0) && (gUI2DialerPara.WlanClient[i].MAC[2]==0) && \
			        (gUI2DialerPara.WlanClient[i].MAC[3]==0) &&(gUI2DialerPara.WlanClient[i].MAC[4]==0) &&(gUI2DialerPara.WlanClient[i].MAC[5]==0) )
				continue;

			for(j=0; j<6; j++)
			{
				if(device->MAC[j] != gUI2DialerPara.WlanClient[i].MAC[j])
					match = 1;

			}

			if(!match)
			{
				memset(&(gUI2DialerPara.WlanClient[i]), 0, sizeof(DeviceNode));
				gUI2DialerPara.ActiveWlanClientNumber--;

				// start to calculate 10mins without client connected so that we can shut down wifi
				if(gUI2DialerPara.ActiveWlanClientNumber == 0)
				{
					//gDialer2UIPara.wifi_start_tim = gDialer2UIPara.wifi_end_tim;
					WIFI_SleepTimer_Change();
				}
				UIRefresh_ClientNum_Change();

				duster_Printf("lwip_delete_WLAN_client: ActiveWlanClientNumber is %d",gUI2DialerPara.ActiveWlanClientNumber);
				break;
			}
		}

        if(device)
            free(device);

	}
#endif
	else
	{
		duster_Printf("%s: incorrect device type", __FUNCTION__);
        if(device)
            free(device);
    }

	CPUartLogPrintf("end wan_delete_device\n");
}

void wan_delete_device6(DeviceNode6 *device, int deviceType)
{
}

void wan_add_device(DeviceNode *device, int deviceType)
{
	OSA_STATUS osa_status;
	WlanClientStatusMessage msg_buf;
#ifdef WIFI_SUPPORT
	DeviceNode *wlan_client_ctx = (DeviceNode *)&gUI2DialerPara.WlanClient;// pointer to wlan client
#endif
	char *mac = NULL;
	int j = 0;

	Duster_module_Printf(2,"%s: add device type[%d],name[%s] mac[%02X:%02X:%02X:%02X:%02X:%02X],ip[%d.%d.%d.%d]",__FUNCTION__ ,\
					  deviceType, strlen(device->name) ? device->name : "NULL",
                      device->MAC[0],device->MAC[1],device->MAC[2],device->MAC[3],device->MAC[4],device->MAC[5],\
					  device->IP[0],device->IP[1], device->IP[2],device->IP[3]);

	Duster_module_Printf(2,"%s: PC name[%s],mac[%02X:%02X:%02X:%02X:%02X:%02X],ip[%d.%d.%d.%d]",__FUNCTION__ ,\
						 strlen(gUI2DialerPara.PC.name) ? gUI2DialerPara.PC.name : "NULL", gUI2DialerPara.PC.MAC[0],gUI2DialerPara.PC.MAC[1],\
	                     gUI2DialerPara.PC.MAC[2],gUI2DialerPara.PC.MAC[3],gUI2DialerPara.PC.MAC[4],gUI2DialerPara.PC.MAC[5],\
	                     gUI2DialerPara.PC.IP[0],gUI2DialerPara.PC.IP[1],gUI2DialerPara.PC.IP[2],gUI2DialerPara.PC.IP[3]);

	if(deviceType == DeviceType_PC)
	{
		if( (memcmp(device->MAC, gUI2DialerPara.PC.MAC, sizeof(device->MAC)) == 0) && (memcmp(device->IP, gUI2DialerPara.PC.IP, sizeof(device->IP)) == 0))
		{
			if((strlen(device->name) == 0) || strcasecmp(device->name,	gUI2DialerPara.PC.name) == 0)
			{
			    duster_Printf("%s: find a same usb record, do not record",	__FUNCTION__);
				if(device)
					free(device);
				return;
			}
		}
		msg_buf.MsgId = lwip_add_PC;

		//UIRefresh_ClientNum_Change();
	}
#ifdef WIFI_SUPPORT
	else
	{
		for(j=0; j< 10; j++)
		{

			if(!is_mac_valid((wlan_client_ctx + j)->MAC))
				continue;
			if(memcmp((wlan_client_ctx + j)->MAC, device->MAC, sizeof(device->MAC)) == 0)
			{
				if(memcmp((wlan_client_ctx + j)->IP, device->IP, sizeof(device->IP)) == 0)
				{
					if((strlen(device->name) == 0) || (strcasecmp(device->name,	(wlan_client_ctx + j)->name) == 0))
					{
					    duster_Printf("%s: find a same wifi record, do not record",	__FUNCTION__);
						if(device)
							free(device);
						return;
					}
				}
			}
		}
		#if 0
		if(mac)
		{
			while(client_list_info)
			{
				if(strncasecmp(client_list_info->MAC,mac, strlen(mac)) == 0)
				{
					duster_Printf("%s: find mac record",__FUNCTION__);
					if(client_list_info->ip)
					{
						if(strcmp(client_list_info->ip,	device->IP) == 0)
						{
							return;
						}
					}
				}
				client_list_info = client_list_info->next;
			}
			duster_free(mac);
			mac = NULL;
		}
		#endif
		msg_buf.MsgId = lwip_add_WLAN_client;
	}
#endif
	msg_buf.MsgData = (CHAR *)device;

	osa_status = OSAMsgQSend(gWlanIndMSGQ, sizeof(WlanClientStatusMessage), (UINT8 *)&msg_buf, OSA_NO_SUSPEND);

	if(osa_status != OS_SUCCESS )
	{
		Duster_module_Printf(1,"%s: OSAMsgQSend fail. osa_status is %u",__FUNCTION__ , osa_status);

		if(device)
			free(device);
	}

}

void wan_add_device6(DeviceNode6 *device, int deviceType)
{
}
/*
void wan_delete_device(DeviceNode *device, int deviceType)
{
        OSA_STATUS osa_status;
        WlanClientStatusMessage msg_buf;

       Duster_module_Printf(2,"%s: delete device, deviceType is %d",__FUNCTION__ ,deviceType);
       Duster_module_Printf(2,"%s: mac is %02X:%02X:%02X:%02X:%02X:%02X",__FUNCTION__ , device->MAC[0],device->MAC[1], \
        	device->MAC[2],device->MAC[3],device->MAC[4],device->MAC[5]);
       Duster_module_Printf(2,"%s: IP is %d.%d.%d.%d",__FUNCTION__ , device->IP[0],device->IP[1], \
        	device->IP[2],device->IP[3]);
       Duster_module_Printf(2,"%s: name is %s",__FUNCTION__ , device->name);

        if(deviceType == DeviceType_PC)
              msg_buf.MsgId = lwip_delete_PC;
        else
        	msg_buf.MsgId = lwip_delete_WLAN_client;

        msg_buf.MsgData = device;

        osa_status = OSAMsgQSend(gWlanIndMSGQ, sizeof(WlanClientStatusMessage), &msg_buf, OSA_NO_SUSPEND);

        if(osa_status != OS_SUCCESS )
        {
             Duster_module_Printf(1,"%s: OSAMsgQSend fail. osa_status is %u",__FUNCTION__ , osa_status);

             if(device)
                free(device);
        }
}
*/

void displayDeviceNode()
{
	char *str=NULL;
	char i;

	duster_Printf("%s: display PC MAC and IP", __FUNCTION__);

	duster_Printf("%s: PC name is %s", __FUNCTION__, gUI2DialerPara.PC.name);

	duster_Printf("%s: IP %d %d %d %d", __FUNCTION__, gUI2DialerPara.PC.IP[0], gUI2DialerPara.PC.IP[1], gUI2DialerPara.PC.IP[2], gUI2DialerPara.PC.IP[3]);
	str = ConvertASCtoString(gUI2DialerPara.PC.IP, ConvertType_IP);
	duster_free(str);
	str= NULL;

	duster_Printf("%s: MAC %x %x %x %x %x %x", __FUNCTION__, gUI2DialerPara.PC.MAC[0], gUI2DialerPara.PC.MAC[1], gUI2DialerPara.PC.MAC[2], gUI2DialerPara.PC.MAC[3],gUI2DialerPara.PC.MAC[4], gUI2DialerPara.PC.MAC[5]);
	str = ConvertASCtoString(gUI2DialerPara.PC.MAC, ConvertType_MAC);
	duster_free(str);
	str= NULL;
#ifdef WIFI_SUPPORT
	for(i=0; i<10; i++)
	{
		duster_Printf("%s: display client%d MAC and IP", __FUNCTION__,i);

		duster_Printf("%s: client%d name is %s", __FUNCTION__, i, gUI2DialerPara.WlanClient[i].name);

		duster_Printf("%s: IP %d %d %d %d", __FUNCTION__, gUI2DialerPara.WlanClient[i].IP[0], gUI2DialerPara.WlanClient[i].IP[1], gUI2DialerPara.WlanClient[i].IP[2], gUI2DialerPara.WlanClient[i].IP[3]);
		str = ConvertASCtoString(gUI2DialerPara.WlanClient[i].IP, ConvertType_IP);
		duster_free(str);
		str= NULL;

		duster_Printf("%s: MAC %x %x %x %x %x %x", __FUNCTION__, gUI2DialerPara.WlanClient[i].MAC[0], gUI2DialerPara.WlanClient[i].MAC[1], gUI2DialerPara.WlanClient[i].MAC[2], gUI2DialerPara.WlanClient[i].MAC[3],gUI2DialerPara.WlanClient[i].MAC[4], gUI2DialerPara.WlanClient[i].MAC[5]);
		str = ConvertASCtoString(gUI2DialerPara.WlanClient[i].MAC, ConvertType_MAC);
		duster_free(str);
		str= NULL;
	}
#endif
}

//ICAT EXPORTED FUNCTION - WAN,APPLET,TEST
void wan_applet_test()
{
	DeviceNode NewDevice;
	DeviceNode *pDevice = NULL;

    memset(&NewDevice, 0 , sizeof(NewDevice));
	memcpy(NewDevice.name, "HFDT_0125", sizeof("HFDT_0125"));
	NewDevice.IP[0] = 0xA;
	NewDevice.IP[1] = 0x1A;
	NewDevice.IP[2] = 0x82;
	NewDevice.IP[3] = 0x63;

	NewDevice.MAC[0] = 0x0;
	NewDevice.MAC[1] = 0x1A;
	NewDevice.MAC[2] = 0x82;
	NewDevice.MAC[3] = 0x3c;
	NewDevice.MAC[4] = 0x0D;
	NewDevice.MAC[5] = 0xF0;

    pDevice = malloc(sizeof(DeviceNode));
    memcpy(pDevice, &NewDevice, sizeof(DeviceNode));
	wan_add_device(pDevice, DeviceType_PC);

	duster_Printf("%s: 1", __FUNCTION__);
	displayDeviceNode();

	memset(&NewDevice, 0 , sizeof(NewDevice));
	memcpy(NewDevice.name, "WLAN_001", sizeof("WLAN_001"));
	NewDevice.IP[0] = 0xA;
	NewDevice.IP[1] = 0x1A;
	NewDevice.IP[2] = 0x82;
	NewDevice.IP[3] = 0x66;

	NewDevice.MAC[0] = 0x01;
	NewDevice.MAC[1] = 0x2A;
	NewDevice.MAC[2] = 0x92;
	NewDevice.MAC[3] = 0x4c;
	NewDevice.MAC[4] = 0x1D;
	NewDevice.MAC[5] = 0xF1;

    pDevice = malloc(sizeof(DeviceNode));
    memcpy(pDevice, &NewDevice, sizeof(DeviceNode));
	wan_add_device(pDevice, DeviceType_WlanClient);


	memset(&NewDevice, 0 , sizeof(NewDevice));
	memcpy(NewDevice.name, "WLAN_002", sizeof("WLAN_002"));
	NewDevice.IP[0] = 0xA;
	NewDevice.IP[1] = 0x1A;
	NewDevice.IP[2] = 0x82;
	NewDevice.IP[3] = 0x70;

	NewDevice.MAC[0] = 0x01;
	NewDevice.MAC[1] = 0x2A;
	NewDevice.MAC[2] = 0x92;
	NewDevice.MAC[3] = 0x4c;
	NewDevice.MAC[4] = 0x1D;
	NewDevice.MAC[5] = 0xF2;

    pDevice = malloc(sizeof(DeviceNode));
    memcpy(pDevice, &NewDevice, sizeof(DeviceNode));
	wan_add_device(pDevice, DeviceType_WlanClient);


	memset(&NewDevice, 0 , sizeof(NewDevice));
	memcpy(NewDevice.name, "WLAN_003", sizeof("WLAN_003"));
	NewDevice.IP[0] = 0xA;
	NewDevice.IP[1] = 0x1A;
	NewDevice.IP[2] = 0x82;
	NewDevice.IP[3] = 0x72;

	NewDevice.MAC[0] = 0x11;
	NewDevice.MAC[1] = 0x3A;
	NewDevice.MAC[2] = 0xa2;
	NewDevice.MAC[3] = 0x5c;
	NewDevice.MAC[4] = 0x2D;
	NewDevice.MAC[5] = 0xF1;

    pDevice = malloc(sizeof(DeviceNode));
    memcpy(pDevice, &NewDevice, sizeof(DeviceNode));
	wan_add_device(pDevice, DeviceType_WlanClient);

	duster_Printf("%s: 2", __FUNCTION__);
	displayDeviceNode();


	memset(&NewDevice, 0 , sizeof(NewDevice));

	NewDevice.MAC[0] = 0x01;
	NewDevice.MAC[1] = 0x2A;
	NewDevice.MAC[2] = 0x92;
	NewDevice.MAC[3] = 0x4c;
	NewDevice.MAC[4] = 0x1D;
	NewDevice.MAC[5] = 0xF1;

    pDevice = malloc(sizeof(DeviceNode));
    memcpy(pDevice, &NewDevice, sizeof(DeviceNode));
	wan_delete_device(pDevice, DeviceType_WlanClient);
	duster_Printf("%s: 3", __FUNCTION__);
	displayDeviceNode();

	memset(&NewDevice, 0 , sizeof(NewDevice));
	memcpy(NewDevice.name, "WLAN_004", sizeof("WLAN_004"));
	NewDevice.IP[0] = 0xA;
	NewDevice.IP[1] = 0x1A;
	NewDevice.IP[2] = 0x82;
	NewDevice.IP[3] = 0x74;

	NewDevice.MAC[0] = 0x11;
	NewDevice.MAC[1] = 0x3A;
	NewDevice.MAC[2] = 0xa2;
	NewDevice.MAC[3] = 0x5c;
	NewDevice.MAC[4] = 0x2D;
	NewDevice.MAC[5] = 0xF2;

    pDevice = malloc(sizeof(DeviceNode));
    memcpy(pDevice, &NewDevice, sizeof(DeviceNode));
	wan_add_device(pDevice, DeviceType_WlanClient);

	duster_Printf("%s: 4", __FUNCTION__);
	displayDeviceNode();
}

char GetWlanClientNum(void)
{
	return gUI2DialerPara.ActiveWlanClientNumber;
}

int GetConnectMode(void)
{
	char *mode_str = NULL;

	mode_str = psm_get_wrapper(PSM_MOD_WAN, "cellular.advanced", "connectmode");

	if(mode_str != NULL)
	{
		gUI2DialerPara.connectmode = (mode_str[0]-'0');
		duster_free(mode_str);
		mode_str = NULL;

	}

	return gUI2DialerPara.connectmode;
}

void readFBFFlash(BOOL flash4bitFlag)
{
#if !defined(CRANE_WEBUI_SUPPORT)
	unsigned int ret=0,i;
	int staus =0;
	FlashLayoutConfInfo * flash_layout = GetFlashLayoutConfig();
	char  *buf = extMalloc(FLASH_BLOCK);

	Duster_module_Printf(1,	"Enter %s",	__FUNCTION__);

	if(buf == NULL)
	{
		Duster_module_Printf(1," malloc size 0x%08x failed", sizeof(s_sd_firmware_flag));
		return;
	}

	//Duster_module_Printf(1,"%s: switch flag in DDR is %x", __FUNCTION__, *(UINT32 *)PLATFORM_MODE_ADDRESS);
	Duster_module_Printf(1,"flash_layout->FBFStartAddress: %x", flash_layout->FBFStartAddress);


	if(flash4bitFlag && (flash_layout->FBFStartAddress >= 0x1000000))		//greater than 16M
		enable_spi_nor_4byte_mode();

	s_sd_firmware_flag *pflag = (s_sd_firmware_flag *)buf;
	memset(buf, 0, FLASH_BLOCK);
	staus = ReadFlash(flash_layout->FBFStartAddress, (UINT_T)buf, sizeof(s_sd_firmware_flag), BOOT_FLASH);  //BOOT_FLASH
	if(staus != NoError)
	{
		Duster_module_Printf(1,"%s read flag failed",__FUNCTION__);
		free(buf);
		buf = NULL;
		return;
	}

	if(flash4bitFlag && (flash_layout->FBFStartAddress >= 0x1000000))		//greater than 16M
		disable_spi_nor_4byte_mode();

	Duster_module_Printf(1,"%s: pflag->header is %X",__FUNCTION__, pflag->header);
	Duster_module_Printf(1,"%s: pflag->upgrade_flag is %X",__FUNCTION__, pflag->upgrade_flag);
	Duster_module_Printf(1,"%s: pflag->fbf_flash_address is %X",__FUNCTION__, pflag->fbf_flash_address);
	Duster_module_Printf(1,"%s: pflag->fbf_file_size is %X",__FUNCTION__, pflag->fbf_file_size);
	Duster_module_Printf(1,"%s: pflag->erase_psm is %X",__FUNCTION__, pflag->erase_psm);
	Duster_module_Printf(1,"%s: pflag->erase_psm_address is %X",__FUNCTION__, pflag->erase_psm_address);
	Duster_module_Printf(1,"%s: pflag->erase_psm_size is %X",__FUNCTION__, pflag->erase_psm_size);
	Duster_module_Printf(1,"%s: pflag->erase_filesystem is %X",__FUNCTION__, pflag->erase_filesystem);
	Duster_module_Printf(1,"%s: pflag->erase_filesystem_address is %X",__FUNCTION__, pflag->erase_filesystem_address);
	Duster_module_Printf(1,"%s: pflag->erase_filesystem_size is %X",__FUNCTION__, pflag->erase_filesystem_size);
	Duster_module_Printf(1,"%s: pflag->upgrade_method is %X",__FUNCTION__, pflag->upgrade_method);
	Duster_module_Printf(1,"%s: pflag->webdata_lzma2 is %X",__FUNCTION__, pflag->webdata_lzma2);
	Duster_module_Printf(1,"%s: pflag->webdata_addrss is %X",__FUNCTION__, pflag->webdata_addrss);
	Duster_module_Printf(1,"%s: pflag->webdata_len is %X",__FUNCTION__, pflag->webdata_len);
	Duster_module_Printf(1,"%s: pflag->wifi_nocal_lzma2 is %X",__FUNCTION__, pflag->wifi_nocal_lzma2);
	Duster_module_Printf(1,"%s: pflag->wifi_nocal_addrss is %X",__FUNCTION__, pflag->wifi_nocal_addrss);
	Duster_module_Printf(1,"%s: pflag->wifi_nocal_len is %X",__FUNCTION__, pflag->wifi_nocal_len);
	Duster_module_Printf(1,"%s: pflag->wifi_lzma2 is %X",__FUNCTION__, pflag->wifi_lzma2);
	Duster_module_Printf(1,"%s: pflag->wifi_addrss is %X",__FUNCTION__, pflag->wifi_addrss);
	Duster_module_Printf(1,"%s: pflag->wifi_len is %X",__FUNCTION__, pflag->wifi_len);
	Duster_module_Printf(1,"%s: pflag->version_flag is %X",__FUNCTION__, pflag->version_flag);

	Duster_module_Printf(5,"%s: pflag->full_system_version is",__FUNCTION__);

	for(i=0; i<64; i++)
	{
		Duster_module_Printf(5,"%X",__FUNCTION__, pflag->full_system_version[i]);

	}

	free(buf);
#endif
}
#if !defined(CRANE_WEBUI_SUPPORT)
BOOL IsDatalinkAllowed(void)
{
	char *str=NULL;
	BOOL allowDatalink = TRUE;

	//allow datalink when both <proto> and <connect_disconnect> are "cellular"
	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "proto");
	if(strcmp(str, "disabled") == 0)
	{
		allowDatalink = FALSE;
		duster_free(str);
		str = NULL;
		Duster_module_Printf(1,	"%s: proto disabled",__FUNCTION__);
		goto end;
	}

	if(str)
	{
		duster_free(str);
		str = NULL;
	}

	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "connect_disconnect");
	if(strcmp(str, "disabled") == 0)
	{
	    Duster_module_Printf(1,	"%s: connect_disconnect is disbled",__FUNCTION__);
		allowDatalink = FALSE;
	}

	if(str)
	{
		duster_free(str);
		str = NULL;
	}

	if(stat_reach_limit())
	{
		allowDatalink = FALSE;
		Duster_module_Printf(1,	"%s: reach statistic limit",__FUNCTION__);
		goto end;
	}
#if 0
	/*dial or not in roaming status*/
	str = psm_get_wrapper(PSM_MOD_WAN, NULL, "Roaming_disable_auto_dial");
	if(str)
	{
		if(strcmp(str,	"1") == 0)
		{
			allowDatalink = FALSE;
 		}
		duster_free(str);
		str = NULL;
	}
#endif

end:
    Duster_module_Printf(1,	"leave %s: %d",	__FUNCTION__,allowDatalink);
	return allowDatalink;
}
#endif
extern PdpLinkTimeInfo PdpTime[CI_PS_MAX_MO_AND_MT_PDP_CTX_NUM];
void update_pdp_conn_time(void)
{
	LinkStatus_Context * pdp_context = NULL;
	int connection_num = 0;

	for(connection_num = 1; connection_num <= MAX_MO_PDP_NUMBER || (connection_num >=MIN_CONNUM_CREATBY_NETWORK && connection_num < MIN_CONNUM_CREATBY_NETWORK+MAX_MT_PDP_NUMBER); connection_num++)
	{
		pdp_context = CM_Get_LinkStatus(connection_num);
		if(pdp_context != NULL)
		{
			if(pdp_context->pdpinfo)
			{
				switch(connection_num){
					case 1:
						gDialer2UIPara.connected_tick1 = PdpTime[pdp_context->pdpinfo->PrimaryCID].total_connected_tick + OSAGetTicks() - PdpTime[pdp_context->pdpinfo->PrimaryCID].connected_tick;
						break;
					case 2:
						gDialer2UIPara.connected_tick2 = PdpTime[pdp_context->pdpinfo->PrimaryCID].total_connected_tick + OSAGetTicks() - PdpTime[pdp_context->pdpinfo->PrimaryCID].connected_tick;
						break;
					case 3:
						gDialer2UIPara.connected_tick3 = PdpTime[pdp_context->pdpinfo->PrimaryCID].total_connected_tick + OSAGetTicks() - PdpTime[pdp_context->pdpinfo->PrimaryCID].connected_tick;
						break;
					case 4:
						gDialer2UIPara.connected_tick4 = PdpTime[pdp_context->pdpinfo->PrimaryCID].total_connected_tick + OSAGetTicks() - PdpTime[pdp_context->pdpinfo->PrimaryCID].connected_tick;
						break;
					case 5:
						gDialer2UIPara.connected_tick5 = PdpTime[pdp_context->pdpinfo->PrimaryCID].total_connected_tick + OSAGetTicks() - PdpTime[pdp_context->pdpinfo->PrimaryCID].connected_tick;
						break;
					case 6:
						gDialer2UIPara.connected_tick6= PdpTime[pdp_context->pdpinfo->PrimaryCID].total_connected_tick + OSAGetTicks() - PdpTime[pdp_context->pdpinfo->PrimaryCID].connected_tick;
						break;
					}
			}

		}

		free_link_status(pdp_context);
	}
}

#ifdef QUECTEL_PROJECT_CUST /* sim manage */
/**
 * add by breeze 2025/01/13
 * note: the current card switch is directly operated by gpio, and after adding AT later, it can also be changed to AT operation.
 */

// Begin Add by ycc, Change sim card logic
/**
 * USIM1 card slot: There are eSIM1 and eSIM2 on the circuit, and here you need to control GPIO_PIN_54 and select one of the cards
 * USIM2 card slot: There are USIMs and eSIM3 on the line, and the default used here is USIM
 * 0 - eSIM1 : GPIO_PIN_54 GPIORC_LOW
 * 1 - eSIM2 : GPIO_PIN_54 GPIORC_HIGH
 * 2 - SIM2
//  * 1 - SIM2
//  * 2 - eSIM2 : GPIO_PIN_54 GPIORC_HIGH
 * -1 - error
 */
// static int get_sim_card()
int get_sim_card(void)
{	
	char resp_str[128] = {0};
	int ret;

	ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "AT*SELECTSIMSLOT?\r", 150, NULL, 1, "+CME ERROR", resp_str, sizeof(resp_str));
	if(0 == ret && sscanf(resp_str, "*SELECTUSIM: %d", &ret) == 1)
	{
		// duster_Printf("%s: AT*SELECTSIMSLOT? %s", __FUNCTION__, resp_str);
		// return (0 == ret) ? (GpioGetLevel(GPIO_PIN_54) ? 2 : 0) : 1;
		return (0 == ret) ? (GpioGetLevel(GPIO_PIN_54) ? 1 : 0) : 2;
	}
	Duster_module_Printf(1,"%s: sim card get error!", __FUNCTION__);
	return -1;
}

// static int set_sim_card(const int sim_card)
int set_sim_card(const int sim_card)
{
	char resp_str[128] = {0};
	int ret;

	if(get_sim_card() == sim_card || -1 == sim_card) return 0;

	Duster_module_Printf(1,"%s: sim_card %d",__FUNCTION__, sim_card);

	// if(1 == sim_card)
	if(2 == sim_card)
	{
		ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "AT*SELECTSIMSLOT=1\r", 150, NULL, 1, "+CME ERROR", resp_str, sizeof(resp_str));
		if(0 != ret && 0 != strcmp(resp_str, "ok"))
		{
			goto SET_SIM_CARD_ERR;
		}
		return 0;
	}

	// if(0 == sim_card || 2 == sim_card)
	if(0 == sim_card || 1 == sim_card)
	{
		ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "AT+CFUN=0\r", 150, NULL, 1, "+CME ERROR", resp_str, sizeof(resp_str));
		if(ret != RESP_OK)
		{
			goto SET_SIM_CARD_ERR;
		}

		if(0 == sim_card)
		{
			GpioSetDirection(GPIO_PIN_54, GPIO_OUT_PIN);
			GpioSetLevel(GPIO_PIN_54, GPIORC_LOW);
		}
		else
		{
			GpioSetDirection(GPIO_PIN_54, GPIO_OUT_PIN);
			GpioSetLevel(GPIO_PIN_54, GPIORC_HIGH);
		}

		ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "AT*SELECTSIMSLOT=0\r", 150, NULL, 1, "+CME ERROR", resp_str, sizeof(resp_str));
		if(0 != ret && 0 != strcmp(resp_str, "ok"))
		{
			goto SET_SIM_CARD_ERR;
		}

		ret = SendATCMDWaitResp(AT_WEBUI_CHANNEL, "AT+CFUN=1\r", 150, NULL, 1, "+CME ERROR", resp_str, sizeof(resp_str));
		if(ret != RESP_OK)
		{
			goto SET_SIM_CARD_ERR;
		}
		// if(0 == sim_card)
		// {
		// 	GpioSetDirection(GPIO_PIN_54, GPIO_OUT_PIN);
		// 	GpioSetLevel(GPIO_PIN_54, GPIORC_LOW);
		// }
		// else
		// {
		// 	GpioSetDirection(GPIO_PIN_54, GPIO_OUT_PIN);
		// 	GpioSetLevel(GPIO_PIN_54, GPIORC_HIGH);
		// }
		return 0;
	}

SET_SIM_CARD_ERR:
	Duster_module_Printf(1,"%s select (e/u)sim%d error!!!",__FUNCTION__, sim_card);
	return -1;
}
// End Add by ycc, Change sim card logic

/**
 * verify sim card password
 */
static int check_sim_password()
{
	Duster_module_Printf(1,"enter %s",__FUNCTION__);

	char *str = NULL;
	char *pwd = NULL;
	int ret = -1; // default check error
	int i;

	str = psm_get_wrapper(PSM_MOD_MANAGEMENT, NULL, "is_verify");
	Duster_module_Printf(1,"%s: is_verify %s",__FUNCTION__, str);
	if(str && 0 == strcmp(str, "1"))
	{
		str = psm_get_wrapper(PSM_MOD_MANAGEMENT, NULL, "sim_password");
		pwd = psm_get_wrapper(PSM_MOD_MANAGEMENT, NULL, "sim_password");
		if(str && pwd)
		{
			// decrypt
			for (i = 0; i < strlen(pwd); i++) 
			{
				pwd[i] = (i%2==0)?pwd[i]-3:pwd[i]-5;
			}
			ret = strcmp(pwd, str);

			Duster_module_Printf(1,"%s: sim_password %s, webui sim_password %s",__FUNCTION__, str, pwd);
		}
	}
	else if(str && 0 == strcmp(str, "0"))
	{
		ret = 0;
	}

	if(str) duster_free(str);
	if(pwd) duster_free(pwd);
	str = NULL;
	pwd = NULL;

	Duster_module_Printf(1,"leave %s",__FUNCTION__);
	return ret;
}

// Begin Add by ycc, Change sim card logic
int sim_manage_post_set(int Action, dc_args_t *dca)
{
	Duster_module_Printf(1,"enter %s",__FUNCTION__);

	int ret;
	// int sim_card;
	// int need_restore = 1; // default restore the previous state
	char *str = NULL;

	// if(0 == check_sim_password())
	// {
		// str = psm_get_wrapper(PSM_MOD_MANAGEMENT, NULL, "sim_card");
		str = psm_get_wrapper(PSM_MOD_SIM_MANAGE, NULL, "sim_card");
		if(str)
		{
			ret = set_sim_card(atoi(str));
			// if(0 == ret)
			// {
				// psm_set_wrapper(PSM_MOD_MANAGEMENT, NULL, "is_verify","0");
				// need_restore = 0;

				// check_SIM_status_simple();
			// }
		}
	// }

	// if(need_restore)
	// {
	// 	// restore the previous state
	// 	sim_card = get_sim_card();
	// 	psm_set_wrapper(PSM_MOD_MANAGEMENT, NULL, "is_verify","1");
	// 	psm_set_wrapper(PSM_MOD_MANAGEMENT, NULL, "sim_card", (0==sim_card?"0":(1==sim_card?"1":(2==sim_card?"2":"0"))));
	// }

	if(str) duster_free(str);
	str = NULL;

	Duster_module_Printf(1,"leave %s",__FUNCTION__);
	// return 0;
	return ret;
}

int sim_manage_post_get(int Action, dc_args_t *dca)
{
	Duster_module_Printf(1,"enter %s",__FUNCTION__);

	if ( !dca )
		return -1;

	if ( dca->dc_type != DUSTER_CB_ARGS_XML )
		return -1;

	if ( !dca->dc_root )
		return -1;

	MrvXMLElement *pChild = NULL;
	int sim_card;
	int nIndex = 0;
	// char *str = NULL;

	sim_card = get_sim_card();
	Duster_module_Printf(1, "%s: get_sim_card=%d", __FUNCTION__, sim_card);
	if(0 <= sim_card)
	{
		pChild = MrvFindElement(dca->dc_root, "sim_card");
		if(pChild)
		{
			for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
			{
				MrvDeleteNode(&pChild->pEntries[nIndex]);
			}
			if (pChild->pEntries)
				duster_free(pChild->pEntries);
			pChild->pEntries = NULL;
			pChild->nMax = 0;
			pChild->nSize = 0;
			MrvAddText(pChild, MrvStrdup((0==sim_card?"0":(1==sim_card?"1":(2==sim_card?"2":"0"))),0), 1);
		}
	}

	// str = psm_get_wrapper(PSM_MOD_MANAGEMENT, NULL, "is_verify");
	// if(str)
	// {
	// 	Duster_module_Printf(1,"%s: is_verify %s",__FUNCTION__, str);

	// 	pChild = MrvFindElement(dca->dc_root, "is_verify");
	// 	if(pChild)
	// 	{
	// 		for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
	// 		{
	// 			MrvDeleteNode(&pChild->pEntries[nIndex]);
	// 		}
	// 		if (pChild->pEntries)
	// 			duster_free(pChild->pEntries);
	// 		pChild->pEntries = NULL;
	// 		pChild->nMax = 0;
	// 		pChild->nSize = 0;
	// 		MrvAddText(pChild, MrvStrdup(str,0), 1);
	// 	}
	// }

	// pChild = MrvFindElement(dca->dc_root, "sim_password");
	// if(pChild)
	// {
	// 	for(nIndex = 0; nIndex<pChild->nSize; nIndex++)
	// 	{
	// 		MrvDeleteNode(&pChild->pEntries[nIndex]);
	// 	}
	// 	if (pChild->pEntries)
	// 		duster_free(pChild->pEntries);
	// 	pChild->pEntries = NULL;
	// 	pChild->nMax = 0;
	// 	pChild->nSize = 0;
	// 	MrvAddText(pChild, MrvStrdup("********",0), 1);
	// }

	// if(str) duster_free(str);
	// str = NULL;

	Duster_module_Printf(1,"leave %s",__FUNCTION__);
	return 0;
}
// End Add by ycc, Change sim card logic

/**
 * @brief get the rsrp dbm of 5G/4G
 * 
 * @return rsrp value, 255 is the exception value
 */
int get_rsrp_dbm(void)
{
	int rsrp_dBm = 255;	// exception value
	INT32 sysMainMode, sysMode;
	struct mmExtendedSignal extendSignal;

	getExtendedSignal(1, &extendSignal);
	getSysMode(1, &sysMainMode, &sysMode);

	if (17 == sysMainMode)
		rsrp_dBm = (255 != extendSignal.rsrp) ? extendSignal.rsrp-140 : extendSignal.rsrp;
	else if (51 == sysMainMode || 52 == sysMainMode)
		rsrp_dBm = (255 != extendSignal.ssRSRP) ? extendSignal.ssRSRP-157 : extendSignal.ssRSRP;

	duster_Printf("%s: extendSignal.rsrp:%d extendSignal.ssRSRP:%d", __FUNCTION__, extendSignal.rsrp, extendSignal.ssRSRP);

	return rsrp_dBm;
}

/**
 * @brief get the rsrq dbm of 5G/4G
 * 
 * @return rsrq value, 255 is the exception value
 */
int get_rsrq_dbm(void)
{
	int rsrq_dBm = 255;	// exception value
	INT32 sysMainMode, sysMode;
	struct mmExtendedSignal extendSignal;

	getExtendedSignal(1, &extendSignal);
	getSysMode(1, &sysMainMode, &sysMode);

	if (17 == sysMainMode)
		rsrq_dBm = (255 != extendSignal.rsrq) ? extendSignal.rsrq/2-43 : extendSignal.rsrq;
	else if (51 == sysMainMode || 52 == sysMainMode)
		rsrq_dBm = (255 != extendSignal.ssRSRQ) ? extendSignal.ssRSRQ/2-43 : extendSignal.ssRSRQ;

	duster_Printf("%s: extendSignal.rsrq:%d extendSignal.ssRSRQ:%d", __FUNCTION__, extendSignal.rsrq, extendSignal.ssRSRQ);

	return rsrq_dBm;
}

/**
 * @brief get the sinr dbm of 5G/4G
 * 
 * @return sinr value, 255 is the exception value
 */
int get_sinr_dbm(void)
{
	int sinr_dBm = 255;	// exception value
	INT32 sysMainMode, sysMode;
	struct mmExtendedSignal extendSignal;

	getExtendedSignal(1, &extendSignal);
	getSysMode(1, &sysMainMode, &sysMode);

	if (17 == sysMainMode)
	sinr_dBm = (255 != extendSignal.sinr) ? extendSignal.sinr/2-23 : extendSignal.sinr;
	else if (51 == sysMainMode || 52 == sysMainMode)
	sinr_dBm = (255 != extendSignal.ssSINR) ? extendSignal.ssSINR/2-23 : extendSignal.ssSINR;

	duster_Printf("%s: extendSignal.sinr:%d extendSignal.ssSINR:%d", __FUNCTION__, extendSignal.sinr, extendSignal.ssSINR);

	return sinr_dBm;
}
#endif

